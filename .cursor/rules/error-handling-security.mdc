---
description:
globs:
alwaysApply: false
---
# Error Handling & Security Standards

## Overview
This project requires robust error handling and comprehensive security measures to protect user data and ensure system reliability.

## Error Handling Standards

### Frontend Error Handling
Always implement proper error handling in Vue components:

```vue
<script setup lang="ts">
import { toast } from '@/composables/useToast'

const isLoading = ref(false)
const error = ref<string | null>(null)

const handleSubmit = handleSubmit(async (values) => {
  isLoading.value = true
  error.value = null

  try {
    await router.post('/users', values, {
      onSuccess: () => {
        toast.success('User created successfully')
        // Handle success
      },
      onError: (errors) => {
        if (errors.message) {
          toast.error(errors.message)
        } else {
          toast.error('Please check the form for errors')
        }
        // Form validation errors are handled automatically by vee-validate
      },
      onFinish: () => {
        isLoading.value = false
      }
    })
  } catch (err) {
    error.value = 'An unexpected error occurred'
    toast.error('An unexpected error occurred')
    console.error('Submission error:', err)
  }
})

// Handle async operations
const fetchData = async () => {
  try {
    const response = await api.get('/data')
    // Handle success
  } catch (err) {
    console.error('Failed to fetch data:', err)
    toast.error('Failed to load data')
  }
}
</script>

<template>
  <form @submit="onSubmit">
    <!-- Show error state -->
    <div v-if="error" class="alert alert-error mb-4">
      {{ error }}
    </div>

    <!-- Form fields -->
    <Button type="submit" :disabled="isLoading">
      {{ isLoading ? 'Processing...' : 'Submit' }}
    </Button>
  </form>
</template>
```

### Backend Error Handling
Implement consistent error handling in controllers:

```php
<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\User;
use App\Http\Requests\StoreUserRequest;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class UserController extends Controller
{
    public function store(StoreUserRequest $request): RedirectResponse
    {
        try {
            DB::beginTransaction();

            $user = User::create($request->validated());

            // Additional operations...

            DB::commit();

            return redirect()
                ->route('users.index')
                ->with('success', 'User created successfully');

        } catch (\Illuminate\Database\QueryException $e) {
            DB::rollback();

            Log::error('Database error during user creation', [
                'error' => $e->getMessage(),
                'user_data' => $request->safe()->only(['name', 'email']),
                'user_id' => auth()->id(),
            ]);

            return back()
                ->withInput()
                ->withErrors(['error' => 'Failed to create user. Please try again.']);

        } catch (\Exception $e) {
            DB::rollback();

            Log::error('Unexpected error during user creation', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => auth()->id(),
            ]);

            return back()
                ->withInput()
                ->withErrors(['error' => 'An unexpected error occurred. Please try again.']);
        }
    }

    public function destroy(User $user): RedirectResponse
    {
        try {
            // Check if user can be deleted
            if ($user->hasRelatedData()) {
                return back()->withErrors([
                    'error' => 'Cannot delete user with existing data. Please remove related records first.'
                ]);
            }

            $user->delete();

            return redirect()
                ->route('users.index')
                ->with('success', 'User deleted successfully');

        } catch (\Exception $e) {
            Log::error('Failed to delete user', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'deleting_user' => auth()->id(),
            ]);

            return back()->withErrors([
                'error' => 'Failed to delete user. Please try again.'
            ]);
        }
    }
}
```

### API Error Responses
Standardize API error responses:

```php
<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class BaseApiController extends Controller
{
    protected function successResponse($data, string $message = null, int $statusCode = 200): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => $data,
            'message' => $message,
        ], $statusCode);
    }

    protected function errorResponse(string $message, int $statusCode = 400, array $errors = []): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => $message,
            'errors' => $errors,
        ], $statusCode);
    }

    protected function validationErrorResponse(array $errors): JsonResponse
    {
        return $this->errorResponse(
            'Validation failed',
            422,
            $errors
        );
    }

    protected function handleApiException(\Exception $e, string $operation): JsonResponse
    {
        Log::error("API error during {$operation}", [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString(),
            'user_id' => auth()->id(),
        ]);

        return $this->errorResponse(
            'An error occurred while processing your request',
            500
        );
    }
}
```

## Security Requirements

### Authentication & Authorization
Always verify user permissions:

```php
<?php

// In Controllers
class UserController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('can:view_user')->only(['index', 'show']);
        $this->middleware('can:create_user')->only(['create', 'store']);
        $this->middleware('can:edit_user')->only(['edit', 'update']);
        $this->middleware('can:delete_user')->only(['destroy']);
    }

    public function index(Request $request)
    {
        // Additional permission checks if needed
        if (!auth()->user()->can('view_user')) {
            abort(403, 'Unauthorized action.');
        }

        // Controller logic...
    }

    public function update(UpdateUserRequest $request, User $user)
    {
        // Check if user can edit this specific record
        if (!auth()->user()->canUpdate($user)) {
            abort(403, 'You cannot edit this user.');
        }

        // Update logic...
    }
}
```

### Route Protection
Protect routes with appropriate middleware:

```php
<?php

// routes/web.php
Route::middleware(['auth', 'verified'])->group(function () {
    // User management routes
    Route::prefix('users')->name('users.')->group(function () {
        Route::get('/', [UserController::class, 'index'])
            ->middleware('can:view_user')
            ->name('index');

        Route::get('/create', [UserController::class, 'create'])
            ->middleware('can:create_user')
            ->name('create');

        Route::post('/', [UserController::class, 'store'])
            ->middleware('can:create_user')
            ->name('store');

        Route::get('/{user}', [UserController::class, 'show'])
            ->middleware('can:view_user')
            ->name('show');

        Route::get('/{user}/edit', [UserController::class, 'edit'])
            ->middleware('can:edit_user')
            ->name('edit');

        Route::put('/{user}', [UserController::class, 'update'])
            ->middleware('can:edit_user')
            ->name('update');

        Route::delete('/{user}', [UserController::class, 'destroy'])
            ->middleware('can:delete_user')
            ->name('destroy');
    });

    // API routes with rate limiting
    Route::prefix('api')->name('api.')->middleware('throttle:60,1')->group(function () {
        Route::delete('users/bulk-delete', [UserController::class, 'bulkDelete'])
            ->middleware('can:delete_user')
            ->name('users.bulk-delete');
    });
});
```

### Data Validation & Sanitization
Always validate and sanitize user input:

```php
<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;

class StoreUserRequest extends FormRequest
{
    public function authorize(): bool
    {
        return $this->user()->can('create_user');
    }

    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255', 'regex:/^[a-zA-Z\s]+$/'],
            'email' => ['required', 'email:rfc,dns', 'max:255', 'unique:users'],
            'password' => ['required', 'confirmed', Password::min(8)->mixedCase()->numbers()->symbols()],
            'status' => ['required', 'in:active,inactive'],
            'role_ids' => ['array'],
            'role_ids.*' => ['integer', 'exists:roles,id'],
        ];
    }

    public function messages(): array
    {
        return User::validationMessages();
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'name' => strip_tags($this->name),
            'email' => strtolower(trim($this->email)),
        ]);
    }
}
```

### File Upload Security
Secure file upload handling:

```php
<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class FileUploadController extends Controller
{
    private const ALLOWED_MIME_TYPES = [
        'image/jpeg',
        'image/png',
        'image/gif',
        'application/pdf',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ];

    private const MAX_FILE_SIZE = 2048; // KB

    public function upload(Request $request)
    {
        $request->validate([
            'file' => [
                'required',
                'file',
                'max:' . self::MAX_FILE_SIZE,
                'mimes:jpeg,png,gif,pdf,xlsx',
                function ($attribute, $value, $fail) {
                    if (!in_array($value->getMimeType(), self::ALLOWED_MIME_TYPES)) {
                        $fail('File type not allowed.');
                    }
                },
            ],
        ]);

        try {
            $file = $request->file('file');

            // Generate secure filename
            $filename = Str::uuid() . '.' . $file->getClientOriginalExtension();

            // Store file securely
            $path = $file->storeAs('uploads', $filename, 'private');

            // Log file upload
            Log::info('File uploaded successfully', [
                'filename' => $filename,
                'original_name' => $file->getClientOriginalName(),
                'size' => $file->getSize(),
                'user_id' => auth()->id(),
            ]);

            return response()->json([
                'success' => true,
                'filename' => $filename,
                'path' => $path,
            ]);

        } catch (\Exception $e) {
            Log::error('File upload failed', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'File upload failed',
            ], 500);
        }
    }
}
```

### Frontend Security
Implement security measures in Vue components:

```vue
<script setup lang="ts">
// Sanitize user input
import DOMPurify from 'dompurify'

const sanitizeInput = (input: string): string => {
  return DOMPurify.sanitize(input, { ALLOWED_TAGS: [] })
}

// Check permissions before showing UI elements
const canEdit = computed(() => {
  return $page.props.auth.user.can.edit_user
})

const canDelete = computed(() => {
  return $page.props.auth.user.can.delete_user
})

// Confirm destructive actions
const handleDelete = (user: User) => {
  if (!canDelete.value) {
    toast.error('You do not have permission to delete users')
    return
  }

  if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
    router.delete(route('users.destroy', user.id), {
      onSuccess: () => toast.success('User deleted successfully'),
      onError: () => toast.error('Failed to delete user')
    })
  }
}
</script>

<template>
  <div>
    <!-- Only show actions if user has permissions -->
    <div v-if="canEdit || canDelete" class="flex gap-2">
      <Button
        v-if="canEdit"
        @click="router.visit(route('users.edit', user.id))"
        variant="outline"
      >
        Edit
      </Button>

      <Button
        v-if="canDelete"
        @click="handleDelete(user)"
        variant="destructive"
      >
        Delete
      </Button>
    </div>

    <!-- Sanitize user-generated content -->
    <div v-html="sanitizeInput(user.description)"></div>
  </div>
</template>
```

## Critical Security Rules

### Authentication Requirements
- **ALWAYS** verify user authentication on protected routes
- **ALWAYS** implement proper session management
- **ALWAYS** use CSRF protection for all forms
- **NEVER** store sensitive data in local storage or cookies
- **ALWAYS** implement rate limiting on API endpoints

### Authorization Requirements
- **ALWAYS** check user permissions before performing actions
- **ALWAYS** validate permissions on both frontend and backend
- **ALWAYS** use role-based access control (RBAC)
- **NEVER** rely on frontend-only permission checks
- **ALWAYS** implement resource-level authorization

### Data Protection Requirements
- **ALWAYS** validate all user inputs on both frontend and backend
- **ALWAYS** sanitize data before database storage
- **ALWAYS** use parameterized queries (Laravel ORM handles this)
- **ALWAYS** escape output to prevent XSS attacks
- **NEVER** trust user input without validation

### File Security Requirements
- **ALWAYS** validate file types and sizes
- **ALWAYS** store uploaded files outside web root
- **ALWAYS** use secure filenames (UUIDs)
- **NEVER** execute uploaded files
- **ALWAYS** scan files for malware if possible

## Security Checklist

### Before Implementing Features
- [ ] Identify authentication requirements
- [ ] Define permission levels needed
- [ ] Plan input validation strategy
- [ ] Consider potential security vulnerabilities
- [ ] Design error handling approach

### During Implementation
- [ ] Apply appropriate middleware to routes
- [ ] Implement permission checks in controllers
- [ ] Validate all user inputs
- [ ] Handle errors gracefully without exposing sensitive information
- [ ] Log security events appropriately

### After Implementation
- [ ] Test authentication and authorization flows
- [ ] Verify input validation is working
- [ ] Test error handling scenarios
- [ ] Review logs for security events
- [ ] Perform security testing (manual and automated)

### Security Testing
- [ ] Test with invalid authentication tokens
- [ ] Attempt to access restricted resources
- [ ] Test input validation with malicious data
- [ ] Verify file upload restrictions
- [ ] Test rate limiting functionality
- [ ] Check for information disclosure in error messages

## Error Logging Standards

### Structured Logging
```php
<?php

// Log security events
Log::channel('security')->warning('Failed login attempt', [
    'email' => $request->email,
    'ip_address' => $request->ip(),
    'user_agent' => $request->userAgent(),
    'timestamp' => now(),
]);

// Log business logic errors
Log::error('User creation failed', [
    'error' => $exception->getMessage(),
    'user_data' => $request->safe()->only(['name', 'email']),
    'user_id' => auth()->id(),
    'ip_address' => $request->ip(),
]);

// Log performance issues
Log::warning('Slow query detected', [
    'query' => $query,
    'execution_time' => $executionTime,
    'user_id' => auth()->id(),
]);
```

### Log Monitoring
- **ALWAYS** monitor logs for security events
- **ALWAYS** set up alerts for critical errors
- **ALWAYS** regularly review error patterns
- **ALWAYS** implement log rotation and archival
- **NEVER** log sensitive data (passwords, tokens)
