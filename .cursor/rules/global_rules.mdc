---
description:
globs:
alwaysApply: false
---
# 🎓 Swinburne Education Management System - Development Standards

## 🏗️ Architecture Overview

**Tech Stack:**
- **Backend**: Lara<PERSON> 12 + PHP 8.4 + FrankenPHP
- **Frontend**: Vue.js 3 + TypeScript + Inertia.js
- **UI Framework**: reka-ui components + TailwindCSS 4.x
- **Form Validation**: vee-validate + Zod schemas
- **Data Tables**: @tanstack/vue-table
- **State Management**: Pinia stores
- **Database**: MySQL 8.0 + Redis cache
- **Deployment**: Docker + Multi-environment setup

## 📁 Project Structure Standards

### Backend Organization
```
app/
├── Http/Controllers/
│   ├── Api/            # API endpoints
│   ├── Auth/           # Authentication
│   ├── Settings/       # System settings
│   ├── Units/          # Unit management
│   └── Users/          # User management
├── Models/             # Eloquent models
├── Services/           # Business logic services
├── Policies/           # Authorization policies
└── Providers/          # Service providers
```

### Frontend Organization
```
resources/js/
├── components/
│   ├── ui/            # reka-ui base components
│   ├── DebouncedInput.vue
│   ├── DataTable.vue
│   └── DataPagination.vue
├── pages/             # Inertia.js pages
├── types/             # TypeScript interfaces
│   ├── models.ts      # Database models
│   ├── validation.ts  # Form validation types
│   └── index.d.ts     # Global types
├── composables/       # Vue composables
├── stores/            # Pinia stores
└── layouts/           # Page layouts
```

## 🎯 Core Development Principles

### 1. Type-First Development
**ALWAYS** define TypeScript interfaces first, then implement:
- Define main model interfaces in `resources/js/types/models.ts`
- Create separate form data interfaces for validation (string fields for form inputs)
- Include optional relationship properties with `?` operator
- Use union types for enums (e.g., `'active' | 'inactive' | 'suspended'`)

### 2. Database Migration Standards

**Field Naming Consistency:**
- Use descriptive, consistent field names: `curriculum_version_id` not `curriculum_id`
- Use specific enum names: `enrollment_status` not just `status`
- Use consistent capacity naming: `max_capacity` not `max_enrollment`
- Add proper constraints: `->constrained()->onDelete('cascade')`

**Migration Dependencies:**
- Order matters - create parent tables first
- Follow naming pattern: `YYYY_MM_DD_HHMMSS_create_table_name.php`
- Programs → Specializations → Curriculum Versions → Students → Course Offerings → Registrations

### 3. Model Relationship Patterns

**Complete Model Definition:**
- Define `$fillable` array with all mass-assignable fields
- Use `$casts` for date fields and enum casting
- Define all `BelongsTo` relationships for parent models
- Define all `HasMany` relationships for child models
- Create static `validationRules()` method returning Laravel validation array
- Use proper validation rules: `required`, `exists:table,column`, `unique:table,field`

## 🎨 Vue.js Component Standards

### 1. Form Components with Validation

**Always use vee-validate + Zod + reka-ui pattern:**
- Import: `useForm` from vee-validate, `toTypedSchema` from @vee-validate/zod, `z` from zod
- Create Zod schema with `toTypedSchema()` wrapper
- Use `useForm()` with `validationSchema` and `initialValues`
- Transform form data before submission (convert strings to numbers, handle null values)
- Use `router[method]()` for submission with proper success/error handling
- Template: Use `<Form>` wrapper with `FormField`, `FormItem`, `FormLabel`, `FormControl`, `FormMessage`
- **CRITICAL**: Never use empty string `value=""` in SelectItem - use `value="none"` instead

### 2. Data Table Components

**Always use DataTable + DataPagination + DebouncedInput pattern:**
```vue
<script setup lang="ts">
import DataTable from '@/components/DataTable.vue'
import DataPagination from '@/components/DataPagination.vue'
import DebouncedInput from '@/components/DebouncedInput.vue'
import type { ColumnDef } from '@tanstack/vue-table'
import type { PaginatedResponse } from '@/types'

interface Props {
  items: PaginatedResponse<YourModel>
  filters?: { search?: string }
}

const props = defineProps<Props>()
const data = computed(() => props.items.data)

// Filters state
const filters = ref({
  search: props.filters?.search || '',
})

// Column definitions with actions slot
const columns: ColumnDef<YourModel>[] = [
  {
    header: 'No',
    id: 'no',
    enableSorting: false,
    cell: ({ row }) => {
      const currentPage = props.items.current_page
      const perPage = props.items.per_page
      return (currentPage - 1) * perPage + row.index + 1
    },
  },
  {
    header: 'Name',
    accessorKey: 'name',
    enableSorting: true,
    cell: ({ row }) => h('div', { class: 'font-medium' }, row.original.name),
  },
  {
    id: 'actions',
    header: 'Actions',
    enableSorting: false,
    cell: 'actions', // Use template slot
  },
]

// Search and pagination handlers
const handleSearch = (value: string) => {
  filters.value.search = String(value)
  router.visit('/your-route', filters.value, {
    preserveState: true,
    preserveScroll: true,
  })
}

const handlePaginationNavigate = (url: string) => {
  router.visit(url, {
    preserveState: true,
    preserveScroll: true,
    only: ['items'],
  })
}
</script>

<template>
  <div class="space-y-4">
    <!-- Filters -->
    <div class="flex items-center gap-4 rounded-lg border p-4">
      <DebouncedInput
        v-model="filters.search"
        @debounced="handleSearch"
        placeholder="Search..."
        class="min-w-[200px] flex-1"
      />
    </div>

    <!-- Table with actions slot -->
    <DataTable :data="data" :columns="columns">
      <template #cell-actions="{ row }">
        <div class="flex items-center gap-2">
          <Button variant="ghost" size="sm" @click="editItem(row.original)">
            <Edit class="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" @click="deleteItem(row.original)">
            <Trash2 class="h-4 w-4" />
          </Button>
        </div>
      </template>
    </DataTable>

    <!-- Pagination -->
    <DataPagination
      :pagination-data="items"
      @navigate="handlePaginationNavigate"
    />
  </div>
</template>
```

### 3. reka-ui Component Usage

**CRITICAL SelectItem Rule**: Never use empty string `value=""` - causes runtime errors. Use meaningful values like `"none"`, `"all"`, or specific identifiers.

## 🚀 Controller Patterns

### 1. Resource Controllers Structure

**Standard Pattern:**
- Use proper type hints: `Request $request`, `Response`, `RedirectResponse`
- Always use `->with()` for eager loading to prevent N+1 queries
- Apply filters with `$request->filled()` and check for `!== 'all'`
- Use `->paginate(15)` for index methods
- Use `->select('id', 'name')` for dropdown data
- Pass `$request->only([...])` for filters to maintain state
- Use Form Request classes for validation: `StoreXRequest`, `UpdateXRequest`
- Check for related records before deletion
- Return proper success/error messages with redirects
## 🚨 Common Pitfalls to Avoid

### 1. Frontend Issues
- ❌ Never use `value=""` in SelectItem components
- ❌ Don't use `watch()` for API calls - use DebouncedInput instead
- ❌ Don't forget to transform form data before submission
- ❌ Always update TypeScript interfaces when changing models

### 2. Backend Issues
- ❌ Don't forget to update both sides of relationships
- ❌ Always validate foreign key constraints in migrations
- ❌ Don't skip validation rules in Form Request classes
- ❌ Always check for related records before deletion

### 3. Database Issues
- ❌ Inconsistent field naming across tables
- ❌ Missing indexes on foreign keys
- ❌ Wrong migration dependencies order
- ❌ Not using proper constraint names

## 📊 Performance Best Practices

### 1. Database Optimization
- Use eager loading: `->with(['relation1', 'relation2'])` to prevent N+1 queries
- Use specific selects: `->select('id', 'name')` for large datasets
- Add proper indexes on foreign keys and frequently filtered columns
- Use compound indexes for multi-column filters

### 2. Frontend Optimization
- Use `computed()` properties for expensive calculations
- Debounce search inputs with DebouncedInput component
- Use `router.visit()` with `preserveState: true, preserveScroll: true`

### 3. Caching Strategy
- Cache frequently accessed data with `Cache::remember()`
- Use Redis for session and cache storage
- Cache dropdown data that doesn't change often

## 🔒 Security Guidelines

### 1. Authorization
- Always use `$this->authorize()` in controller methods
- Create policies for complex authorization logic
- Use Gates for system-wide permissions

### 2. Input Validation
- Always use Form Request classes for validation
- Reference model validation rules: `Student::validationRules()`
- Include authorization in Form Request `authorize()` method

### 3. Data Protection
- Use `$fillable` instead of `$guarded` for mass assignment protection
- Use `$hidden` array to hide sensitive fields from JSON output
- Never expose sensitive data in API responses

---
## 📝 Summary

This development standards document reflects the actual architecture and patterns used in the Swinburne Education Management System. Following these guidelines ensures:

- **Type Safety**: Complete TypeScript coverage
- **Performance**: Optimized queries and caching
- **Consistency**: Standardized patterns across the codebase
- **Security**: Proper authorization and validation
- **Maintainability**: Clear structure and documentation
- **Quality**: Automated testing and code formatting

**Key Principles:**
1. **Type-First Development** - Define interfaces before implementation
2. **Component Reusability** - Use DebouncedInput, DataTable, DataPagination
3. **Validation Alignment** - Sync Zod schemas with Laravel validation
4. **Database Consistency** - Proper relationships and naming
5. **Error Handling** - Comprehensive logging and user feedback

Follow these standards to maintain code quality and team productivity! 🚀

