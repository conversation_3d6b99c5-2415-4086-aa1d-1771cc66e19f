---
description:
globs:
alwaysApply: false
---
# Naming Conventions

## Overview
Consistent naming conventions are critical for code readability, maintainability, and team collaboration across the Laravel + Vue.js + Inertia.js stack.

## Files & Directories

### Controllers
- **Pattern**: PascalCase with `Controller` suffix
- **Location**: [app/Http/Controllers/](mdc:app/Http/Controllers)
- **Examples**:
  ```
  UserController.php
  SpecializationController.php
  CurriculumVersionController.php
  ```

### Multi-Controller Features
- **Pattern**: Group in folders under [app/Http/Controllers/](mdc:app/Http/Controllers)
- **Examples**:
  ```
  Users/
    UserController.php
    UserProfileController.php
    UserPreferencesController.php
  Settings/
    GeneralSettingsController.php
    NotificationSettingsController.php
  ```

### Models
- **Pattern**: PascalCase singular
- **Location**: [app/Models/](mdc:app/Models)
- **Examples**:
  ```
  User.php
  CurriculumVersion.php
  Specialization.php
  Program.php
  ```

### Vue Components
- **Pattern**: PascalCase
- **Location**: [resources/js/components/](mdc:resources/js/components)
- **Examples**:
  ```
  DataTable.vue
  UserProfile.vue
  DebouncedInput.vue
  FormField.vue
  ```

### Vue Pages
- **Pattern**: PascalCase
- **Location**: [resources/js/pages/](mdc:resources/js/pages)
- **Structure**:
  ```
  pages/
    Users/
      Index.vue
      Create.vue
      Edit.vue
      Show.vue
    Specializations/
      Index.vue
      Create.vue
      Edit.vue
  ```

### Routes
- **Pattern**: kebab-case for URLs
- **Examples**:
  ```
  /users
  /curriculum-versions
  /specializations
  /user-profiles
  ```

## Variables & Functions

### PHP (Laravel)
- **Methods**: camelCase
- **Properties**: snake_case
- **Constants**: SCREAMING_SNAKE_CASE
- **Examples**:
  ```php
  // Methods
  public function createUser()
  public function getUserProfile()
  public function validateSpecialization()

  // Properties
  protected $table = 'curriculum_versions';
  protected $fillable = ['name', 'email', 'created_at'];

  // Constants
  const MAX_UPLOAD_SIZE = 2048;
  const DEFAULT_STATUS = 'active';
  ```

### TypeScript/JavaScript (Vue)
- **Variables**: camelCase
- **Functions**: camelCase
- **Constants**: SCREAMING_SNAKE_CASE or camelCase for objects
- **Examples**:
  ```typescript
  // Variables
  const userName = ref('')
  const isLoading = ref(false)
  const usersList = ref<User[]>([])

  // Functions
  const handleSubmit = () => {}
  const validateForm = () => {}
  const fetchUserData = async () => {}

  // Constants
  const MAX_FILE_SIZE = 1024
  const validationRules = {
    name: { required: true, minLength: 1 }
  }
  ```

### Vue Props
- **Script**: camelCase
- **Template**: kebab-case
- **Examples**:
  ```vue
  <script setup lang="ts">
  interface Props {
    showActions: boolean
    userProfile: UserProfile
    isLoading: boolean
  }
  </script>

  <template>
    <UserCard
      :show-actions="true"
      :user-profile="profile"
      :is-loading="loading"
    />
  </template>
  ```

## Database Conventions

### Tables
- **Pattern**: snake_case plural
- **Examples**:
  ```
  users
  curriculum_versions
  specializations
  user_profiles
  ```

### Columns
- **Pattern**: snake_case
- **Examples**:
  ```sql
  id
  name
  email
  created_at
  updated_at
  user_id
  is_active
  ```

### Foreign Keys
- **Pattern**: `{model}_id`
- **Examples**:
  ```sql
  user_id
  program_id
  specialization_id
  curriculum_version_id
  ```

### Pivot Tables
- **Pattern**: `{model1}_{model2}` (alphabetical order)
- **Examples**:
  ```
  role_user
  permission_role
  program_specialization
  ```

## Constants & Types

### TypeScript Interfaces
- **Pattern**: PascalCase
- **Location**: [resources/js/types/](mdc:resources/js/types)
- **Examples**:
  ```typescript
  User
  PaginatedResponse
  SpecializationFormData
  ApiResponse
  ValidationRules
  ```

### Validation Rules
- **Pattern**: camelCase keys in nested objects
- **Location**: [resources/js/types/validation.ts](mdc:resources/js/types/validation.ts)
- **Examples**:
  ```typescript
  export const ValidationRules = {
    user: {
      name: { minLength: 1, maxLength: 255 },
      email: { maxLength: 255 },
      password: { minLength: 8 }
    },
    specialization: {
      name: { minLength: 1, maxLength: 255 },
      code: { minLength: 1, maxLength: 10 }
    }
  }
  ```

### Route Names
- **Pattern**: dot notation with snake_case
- **Examples**:
  ```php
  users.index
  users.create
  users.store
  users.show
  users.edit
  users.update
  users.destroy
  specializations.index
  curriculum_versions.create
  ```

## Event & Method Naming

### Vue Events
- **Pattern**: kebab-case for custom events
- **Examples**:
  ```vue
  <script setup lang="ts">
  interface Emits {
    'user-updated': [user: User]
    'form-submitted': [data: FormData]
    'page-changed': [page: number]
  }

  const emit = defineEmits<Emits>()
  </script>

  <template>
    <UserForm @user-updated="handleUserUpdate" />
    <DataPagination @page-changed="handlePageChange" />
  </template>
  ```

### Vue Methods
- **Pattern**: camelCase with descriptive prefixes
- **Examples**:
  ```typescript
  // Event handlers
  const handleSubmit = () => {}
  const handleDelete = () => {}
  const handlePageChange = () => {}

  // API calls
  const fetchUsers = () => {}
  const createUser = () => {}
  const updateUser = () => {}

  // Utility functions
  const validateForm = () => {}
  const formatDate = () => {}
  const transformData = () => {}
  ```

## Class & Service Naming

### Laravel Services
- **Pattern**: PascalCase with `Service` suffix
- **Location**: `app/Services/`
- **Examples**:
  ```
  UserService.php
  EmailService.php
  ReportGeneratorService.php
  ```

### Form Requests
- **Pattern**: `{Action}{Model}Request`
- **Location**: `app/Http/Requests/`
- **Examples**:
  ```
  StoreUserRequest.php
  UpdateUserRequest.php
  StoreSpecializationRequest.php
  ```

### Middleware
- **Pattern**: PascalCase descriptive names
- **Location**: `app/Http/Middleware/`
- **Examples**:
  ```
  EnsureUserHasPermission.php
  CheckUserStatus.php
  ValidateApiToken.php
  ```

## Composable Naming

### Vue Composables
- **Pattern**: camelCase with `use` prefix
- **Location**: [resources/js/composables/](mdc:resources/js/composables)
- **Examples**:
  ```typescript
  // File: useUsers.ts
  export function useUsers() {
    // composable logic
  }

  // File: useValidation.ts
  export function useValidation() {
    // validation logic
  }

  // File: useApi.ts
  export function useApi() {
    // API utilities
  }
  ```

## Critical Rules

### Consistency Requirements
- **ALWAYS** follow the established patterns in existing code
- **NEVER** mix naming conventions within the same context
- **ALWAYS** use descriptive names that convey purpose
- **NEVER** use abbreviations unless they're widely understood
- **ALWAYS** check existing codebase for similar patterns

### Language-Specific Rules
- **PHP**: Follow PSR-1 and PSR-12 standards
- **JavaScript/TypeScript**: Follow standard camelCase conventions
- **Vue**: Use kebab-case in templates, camelCase in scripts
- **CSS**: Use kebab-case for class names
- **Database**: Use snake_case for all identifiers

### File Organization Rules
- **ALWAYS** place files in the correct directory structure
- **ALWAYS** use singular names for models
- **ALWAYS** use plural names for database tables
- **ALWAYS** group related files in appropriate folders

## Naming Checklist

### Before Creating New Files/Classes
- [ ] Check existing codebase for similar patterns
- [ ] Follow the appropriate naming convention for the file type
- [ ] Ensure the name clearly describes the purpose
- [ ] Place the file in the correct directory
- [ ] Use appropriate prefixes/suffixes for the file type
- [ ] Verify naming consistency with related files
- [ ] Update any necessary imports or references
- [ ] Check that the name follows language-specific conventions

### Common Naming Mistakes to Avoid
- ❌ Using camelCase for database columns
- ❌ Using snake_case for TypeScript variables
- ❌ Mixing singular and plural in model names
- ❌ Using abbreviations in interface names
- ❌ Inconsistent event naming across components
- ❌ Using generic names like `data`, `item`, `thing`
- ❌ Not following established route naming patterns
- ❌ Using different conventions in the same file
