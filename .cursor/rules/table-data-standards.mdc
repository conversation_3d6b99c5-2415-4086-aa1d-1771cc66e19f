---
description:
globs:
alwaysApply: false
---
# Table Display Standards

## Overview
All tabular data in this project must use standardized components for consistency, performance, and maintainability.

## DataTable Implementation (MANDATORY)

### Required Components
**ALWAYS** use [DataTable.vue](mdc:resources/js/components/DataTable.vue) and [DataPagination.vue](mdc:resources/js/components/DataPagination.vue):

```vue
<script setup lang="ts">
import DataTable from '@/components/DataTable.vue'
import DataPagination from '@/components/DataPagination.vue'
import type { ColumnDef } from '@tanstack/vue-table'
import type { User } from '@/types'

interface Props {
  users: {
    data: User[]
    meta: {
      current_page: number
      per_page: number
      total: number
      last_page: number
      from: number | null
      to: number | null
    }
    links: {
      first: string | null
      last: string | null
      prev: string | null
      next: string | null
    }
  }
}

const props = defineProps<Props>()

const columns: ColumnDef<User>[] = [
  {
    accessorKey: 'name',
    header: 'Name',
  },
  {
    accessorKey: 'email',
    header: 'Email',
  },
  {
    accessorKey: 'created_at',
    header: 'Created At',
    cell: ({ row }) => formatDate(row.original.created_at),
  },
  {
    id: 'actions',
    header: 'Actions',
    cell: ({ row }) => h(UserActions, { user: row.original }),
  },
]
</script>

<template>
  <div class="space-y-4">
    <DataTable
      :data="users.data"
      :columns="columns"
      :loading="false"
    />
    <DataPagination
      :current-page="users.meta.current_page"
      :per-page="users.meta.per_page"
      :total="users.meta.total"
      :last-page="users.meta.last_page"
      @page-change="handlePageChange"
    />
  </div>
</template>
```

## Required Table Features

### Pagination Handling
```vue
<script setup lang="ts">
const handlePageChange = (page: number) => {
  router.get(route('users.index'), { page }, {
    preserveState: true,
    preserveScroll: true
  })
}
</script>
```

### Search and Filtering
Always use [DebouncedInput.vue](mdc:resources/js/components/DebouncedInput.vue) for search functionality:

```vue
<script setup lang="ts">
import DebouncedInput from '@/components/DebouncedInput.vue'

const filters = ref({
  search: '',
  status: 'all',
  role: 'all'
})

const handleSearch = (value: string) => {
  filters.value.search = value
  updateFilters()
}

const updateFilters = () => {
  const filterParams = {
    search: filters.value.search,
    status: filters.value.status === 'all' ? '' : filters.value.status,
    role: filters.value.role === 'all' ? '' : filters.value.role,
  }

  router.get(route('users.index'), filterParams, {
    preserveState: true,
    preserveScroll: true
  })
}
</script>

<template>
  <div class="flex gap-4 mb-6">
    <DebouncedInput
      v-model="filters.search"
      @debounced="handleSearch"
      placeholder="Search users..."
      class="max-w-sm"
    />

    <Select v-model="filters.status" @update:model-value="updateFilters">
      <SelectTrigger class="w-[180px]">
        <SelectValue placeholder="All Statuses" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="all">All Statuses</SelectItem>
        <SelectItem value="active">Active</SelectItem>
        <SelectItem value="inactive">Inactive</SelectItem>
      </SelectContent>
    </Select>
  </div>
</template>
```

### Actions Column Pattern
Create separate action components for table actions:

```vue
<!-- UserActions.vue -->
<script setup lang="ts">
import type { User } from '@/types'

interface Props {
  user: User
}

const props = defineProps<Props>()

const handleEdit = () => {
  router.visit(route('users.edit', props.user.id))
}

const handleDelete = () => {
  if (confirm('Are you sure you want to delete this user?')) {
    router.delete(route('users.destroy', props.user.id), {
      onSuccess: () => toast.success('User deleted successfully'),
      onError: () => toast.error('Failed to delete user')
    })
  }
}
</script>

<template>
  <div class="flex items-center gap-2">
    <Button
      variant="ghost"
      size="sm"
      @click="handleEdit"
      v-if="$page.props.auth.user.can.edit_user"
    >
      <Edit2 class="h-4 w-4" />
    </Button>

    <Button
      variant="ghost"
      size="sm"
      @click="handleDelete"
      v-if="$page.props.auth.user.can.delete_user"
    >
      <Trash2 class="h-4 w-4" />
    </Button>
  </div>
</template>
```

## Bulk Operations

### Selection and Bulk Actions
```vue
<script setup lang="ts">
const selectedRows = ref<number[]>([])

const handleBulkDelete = () => {
  if (selectedRows.value.length === 0) {
    toast.error('Please select items to delete')
    return
  }

  if (confirm(`Are you sure you want to delete ${selectedRows.value.length} items?`)) {
    router.delete('/api/users/bulk-delete', {
      data: { ids: selectedRows.value },
      onSuccess: () => {
        toast.success('Items deleted successfully')
        selectedRows.value = []
      },
      onError: () => toast.error('Failed to delete items')
    })
  }
}

const columns: ColumnDef<User>[] = [
  {
    id: 'select',
    header: ({ table }) => h(Checkbox, {
      checked: table.getIsAllPageRowsSelected(),
      'onUpdate:checked': (value: boolean) => table.toggleAllPageRowsSelected(!!value),
    }),
    cell: ({ row }) => h(Checkbox, {
      checked: row.getIsSelected(),
      'onUpdate:checked': (value: boolean) => row.toggleSelected(!!value),
    }),
  },
  // ... other columns
]
</script>

<template>
  <div class="space-y-4">
    <!-- Bulk Actions -->
    <div v-if="selectedRows.length > 0" class="flex items-center gap-2">
      <span class="text-sm text-muted-foreground">
        {{ selectedRows.length }} item(s) selected
      </span>
      <Button
        variant="destructive"
        size="sm"
        @click="handleBulkDelete"
      >
        Delete Selected
      </Button>
    </div>

    <DataTable
      :data="users.data"
      :columns="columns"
      v-model:row-selection="selectedRows"
    />
  </div>
</template>
```

## Export Functionality

### Excel Export Pattern
```vue
<script setup lang="ts">
const handleExport = () => {
  const exportData = {
    format: 'excel',
    filters: filters.value
  }

  router.post('/api/users/export', exportData, {
    onSuccess: () => toast.success('Export started, file will download shortly'),
    onError: () => toast.error('Failed to export data')
  })
}
</script>

<template>
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-semibold">Users</h1>

    <div class="flex gap-2">
      <Button @click="handleExport" variant="outline">
        <Download class="h-4 w-4 mr-2" />
        Export
      </Button>

      <Button @click="router.visit(route('users.create'))">
        <Plus class="h-4 w-4 mr-2" />
        Add User
      </Button>
    </div>
  </div>
</template>
```

## Backend Requirements

### Controller Pagination
```php
<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;

class UserController extends Controller
{
    public function index(Request $request)
    {
        $query = User::query();

        // Apply search filter
        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%');
            });
        }

        // Apply status filter
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Apply pagination
        $users = $query->paginate(15)->withQueryString();

        return inertia('Users/Index', [
            'users' => $users,
            'filters' => $request->only(['search', 'status']),
        ]);
    }
}
```

## Critical Rules

### Table Requirements
- **ALWAYS** use DataTable.vue for all tabular data
- **ALWAYS** implement proper pagination with preserveState and preserveScroll
- **ALWAYS** use DebouncedInput for search functionality
- **NEVER** use empty string values in Select components
- **ALWAYS** handle loading states during operations
- **ALWAYS** provide user feedback with toast notifications

### Performance Requirements
- **ALWAYS** implement server-side pagination for large datasets
- **ALWAYS** use debounced search to prevent excessive API calls
- **ALWAYS** preserve state when navigating or filtering
- **NEVER** load entire datasets in frontend

### User Experience Requirements
- **ALWAYS** show loading states during operations
- **ALWAYS** provide clear feedback for user actions
- **ALWAYS** implement proper error handling
- **ALWAYS** ask for confirmation on destructive actions
- **ALWAYS** clear selections after bulk operations

## Table Implementation Checklist

### Before Creating Tables
- [ ] Define proper TypeScript interfaces for data
- [ ] Set up column definitions with proper types
- [ ] Implement search and filtering
- [ ] Add pagination handling
- [ ] Create action components
- [ ] Test all user interactions
- [ ] Implement bulk operations if needed
- [ ] Add export functionality if required
- [ ] Test performance with large datasets
- [ ] Verify responsive design
