# Code Structure and Conventions

## Architecture Pattern: Service-Request-Resource
**MANDATORY**: All modules must follow this exact pattern:

### Required Folder Structure
- `app/Http/Controllers/Web` - Web controllers using Inertia
- `app/Http/Controllers/Api` - API controllers returning JSON 
- `app/Services` - Business logic services
- `app/Http/Requests/<ModuleName>` - Form validation requests
- `app/Http/Resources/<ModuleName>` - API response resources

### Implementation Rules
1. **Business logic MUST reside in Service classes**
2. **Controllers coordinate only: accept request → call service → return result**
3. **No model logic directly in controllers**
4. **Web and API controllers must be clearly separated**
5. **Each module needs Service + FormRequest + Resource + Controllers**

## Naming Conventions
- **Controllers**: PascalCase with `Controller` suffix
- **Models**: PascalCase singular (e.g., `User.php`)
- **Vue Components**: PascalCase (e.g., `DataTable.vue`)
- **Routes**: kebab-case (e.g., `/curriculum-versions`)
- **Database**: snake_case for tables and columns
- **Files**: Follow Laravel/Vue conventions

## Frontend Structure
```
resources/js/
├── pages/              # Vue pages by feature
├── components/         # Reusable components
│   ├── ui/            # Reka-ui components (auto-generated)
│   └── ...            # Custom components
├── types/             # TypeScript interfaces (CENTRAL LOCATION)
├── composables/       # Reusable logic
├── constants/         # Route constants and configurations
├── layouts/           # Page layouts
├── stores/            # Pinia stores
└── utils/             # Utility functions
```

## Type Management (MANDATORY)
- Store ALL interfaces in `resources/js/types/` directory
- Match backend model structure exactly
- Use proper date types (string for ISO dates)
- Define union types for enums and status fields
