# Development Commands and Workflow

## Development Environment

### Quick Start (Recommended)
```bash
# Docker development (recommended)
./dev.sh start              # Start all services with Docker

# Traditional development  
composer dev                # Start Laravel + Vue + Queue + Logs
```

### Individual Commands
```bash
# Laravel Backend
php artisan serve           # Development server
php artisan migrate        # Run migrations
php artisan migrate:fresh --seed  # Fresh database with seeders
php artisan queue:work     # Process queues
php artisan pail           # View logs in real-time

# Frontend Development
npm run dev                # Vite dev server with HMR
npm run build              # Production build
npm run build:ssr          # Build with SSR support
```

## Code Quality Commands

### Testing
```bash
php artisan test           # Run all tests (Pest)
./scripts/pre-push.sh      # Fast local tests
./scripts/test-local.sh    # Comprehensive Docker tests
```

### Code Style & Linting
```bash
# PHP
vendor/bin/pint            # Laravel Pint (code formatting)
vendor/bin/pint --test     # Check formatting without fixing

# Frontend
npm run lint               # ESLint (auto-fix)
npm run format             # Prettier formatting
npm run format:check       # Check formatting
npm run type-check         # TypeScript compilation check
```

### Combined Quality Check
```bash
composer ci                # Run all CI checks (pint, pest, npm lint/format/type-check)
npm run pre-push           # Full pre-push validation
```

## Docker Commands
```bash
# Development
./dev.sh start             # Start development environment
./dev.sh stop              # Stop development environment
./dev.sh logs              # View logs

# Local Production Testing
./local-prod.sh start      # Start production-like environment
./local-prod.sh stop       # Stop local production

# Production Deployment
./prod.sh deploy           # Deploy to production
```

## Database Commands
```bash
php artisan migrate        # Run pending migrations
php artisan db:seed        # Run seeders
php artisan migrate:fresh --seed  # Fresh install with data
./scripts/backup-database.sh      # Backup database
```

## Project Setup Commands
```bash
./scripts/setup-project.sh    # Initial project setup
./scripts/validate-setup.sh   # Validate installation
npm run setup                  # Same as setup-project.sh
```