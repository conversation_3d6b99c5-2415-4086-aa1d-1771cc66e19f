# What to Do When a Task is Completed

## Pre-Completion Checklist

### 1. Code Quality Verification
```bash
# Run all code quality checks
composer ci                   # Runs pint, pest, and npm checks
# Or run individually:
vendor/bin/pint --test        # Check PHP formatting
php artisan test              # Run all tests
npm run lint                  # Check JS/Vue/TS code
npm run format:check          # Check formatting
npm run type-check            # TypeScript compilation
```

### 2. Manual Testing
- [ ] Test functionality in browser manually
- [ ] Check responsive design on different screen sizes
- [ ] Test form validation (both frontend and backend)
- [ ] Verify error handling and user feedback
- [ ] Check for console errors and warnings
- [ ] Test permissions and authorization flows

### 3. Integration Testing
```bash
./scripts/pre-push.sh         # Quick integration tests
./scripts/test-local.sh       # Comprehensive Docker tests
```

## Documentation Updates

### 4. Update Documentation (if needed)
- [ ] Update API documentation for new endpoints
- [ ] Add/update technical documentation in `docs/technical/`
- [ ] Update user guide if UI changes affect users
- [ ] Document any new environment variables in `.env.example`

### 5. Type Definitions (MANDATORY for Frontend)
- [ ] Add/update TypeScript interfaces in `resources/js/types/`
- [ ] Ensure type consistency between frontend and backend
- [ ] Update API response types if endpoints changed

## Code Review Preparation

### 6. Code Organization
- [ ] Follow Service-Request-Resource pattern
- [ ] Ensure proper separation of concerns
- [ ] Check naming conventions are followed
- [ ] Verify file organization matches project structure

### 7. Security Review
- [ ] Validate all user inputs (frontend + backend)
- [ ] Check authorization middleware on protected routes
- [ ] Ensure CSRF protection on forms
- [ ] Verify SQL injection prevention

## Deployment Preparation

### 8. Environment Configuration
- [ ] Check if new environment variables are needed
- [ ] Update `.env.example` with new variables
- [ ] Document any configuration changes

### 9. Database Changes
- [ ] Ensure migrations are reversible
- [ ] Test migration rollback if applicable
- [ ] Update seeder data if schema changed
- [ ] Verify foreign key constraints

## Final Validation

### 10. Performance Check
- [ ] Check for N+1 queries (use Laravel Debugbar)
- [ ] Verify proper pagination on large datasets
- [ ] Test loading states and error boundaries
- [ ] Check bundle size impact (frontend)

### 11. Cross-browser Testing (if UI changes)
- [ ] Test in Chrome, Firefox, Safari
- [ ] Verify mobile responsiveness
- [ ] Check accessibility compliance

## Pre-Commit Commands
```bash
# Run the comprehensive pre-push script
./scripts/pre-push.sh

# Or for Docker environment
./scripts/pre-push.sh --docker
```

## Git Workflow
```bash
# After all checks pass:
git add .
git commit -m "feat: descriptive commit message"
git push origin feature-branch

# Create pull request with:
# - Description of changes
# - Testing performed
# - Screenshots (if UI changes)
# - Breaking changes (if any)
```

## Deployment Notes

### 12. Production Considerations
- [ ] Feature flags ready (if applicable)
- [ ] Database backups planned (if schema changes)
- [ ] Rollback plan documented
- [ ] Monitoring/alerts configured for new features

---

**Remember**: The pre-push script (`./scripts/pre-push.sh`) automates most of these checks, but manual testing and documentation review are still essential!