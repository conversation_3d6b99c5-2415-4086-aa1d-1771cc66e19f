# Swinx - Swinburne Project Management System

## Purpose
Modern web application built for comprehensive educational management at Swinburne University. The system provides multi-campus support, role-based access control, and complete user management capabilities.

## Key Features
- **Multi-campus Support**: Manage multiple university campuses
- **Role-based Access Control**: Granular permissions system 
- **User Management**: Complete CRUD with import/export functionality
- **Modern UI**: Responsive design with Vue.js 3 and TailwindCSS
- **RESTful API**: Full API for external integrations
- **Docker Ready**: Complete containerization for all environments

## System Architecture
```
Internet → FrankenPHP (SSL/HTTP) → Laravel App → MySQL/Redis
                                ↓
                          Queue Workers & Scheduler
```

## Environment Types
- **Development**: HTTP-only, debug enabled, hot reload
- **Local Production**: HTTPS testing, production-like environment  
- **Production**: Full HTTPS, SSL certificates, optimized performance