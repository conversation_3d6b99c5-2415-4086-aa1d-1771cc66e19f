# Essential Development Commands for Swinx Project

## 🚀 Quick Start Commands (Darwin/macOS)

### Start Development
```bash
# Recommended: Docker development environment
./dev.sh start

# Alternative: Traditional development (requires local PHP/MySQL)
composer dev
```

### Stop Development
```bash
./dev.sh stop              # Stop Docker environment
# Traditional: Ctrl+C in terminal running composer dev
```

## 📋 Daily Development Commands

### Backend (Laravel)
```bash
php artisan serve          # Start development server (if not using Docker)
php artisan migrate        # Run database migrations
php artisan migrate:fresh --seed  # Fresh database with sample data
php artisan tinker         # Interactive PHP console
php artisan route:list     # View all routes
php artisan queue:work     # Process background jobs
```

### Frontend (Vue.js)
```bash
npm run dev               # Start Vite dev server with hot reload
npm run build             # Build for production
npm run type-check        # TypeScript type checking
```

## 🧪 Testing & Quality Assurance

### Run Tests
```bash
php artisan test          # Run all tests (Pest PHP)
php artisan test --filter UserTest  # Run specific test file
./scripts/pre-push.sh     # Quick pre-commit validation
./scripts/test-local.sh   # Full Docker testing suite
```

### Code Quality
```bash
vendor/bin/pint           # Format PHP code (Laravel Pint)
npm run lint              # Lint and fix JavaScript/Vue
npm run format            # Format with Prettier
composer ci               # Run all CI checks
```

## 🗄️ Database Management

### Migrations & Seeding
```bash
php artisan migrate                    # Run pending migrations
php artisan migrate:rollback           # Rollback last migration
php artisan db:seed                    # Run database seeders
php artisan migrate:fresh --seed       # Fresh install with sample data
```

### Database Utilities
```bash
./scripts/backup-database.sh          # Backup database
php artisan db:show                    # Show database info
```

## 🐳 Docker Operations (Recommended)

### Development Environment
```bash
./dev.sh start            # Start all services (app, database, redis)
./dev.sh stop             # Stop all services  
./dev.sh logs             # View application logs
./dev.sh restart          # Restart services
```

### Local Production Testing
```bash
./local-prod.sh start     # Start production-like environment
./local-prod.sh stop      # Stop production environment
```

## 🛠️ Project Setup & Maintenance

### Initial Setup
```bash
./scripts/setup-project.sh    # Complete project setup
./scripts/validate-setup.sh   # Validate installation
cp .env.example .env           # Copy environment file
php artisan key:generate       # Generate app key
```

### Maintenance
```bash
composer install              # Install PHP dependencies
npm install                   # Install Node.js dependencies
php artisan config:clear      # Clear config cache
php artisan route:clear       # Clear route cache
```

## 🔧 macOS-Specific Utilities

### File Operations
```bash
find . -name "*.php" -type f   # Find PHP files
grep -r "pattern" app/         # Search in app directory
ls -la                         # List files with details
```

### Process Management
```bash
lsof -i :8000                 # Check what's using port 8000
ps aux | grep php             # Find PHP processes
kill -9 <PID>                 # Kill specific process
```

### System Info
```bash
php -v                        # PHP version
node -v                       # Node.js version
docker --version              # Docker version
git status                    # Git repository status
```

## 🚨 Emergency Commands

### Reset Development Environment
```bash
./dev.sh stop                 # Stop Docker
docker system prune -f        # Clean Docker
./dev.sh start                # Restart fresh
```

### Fix Common Issues
```bash
php artisan config:clear      # Clear config cache
composer dump-autoload       # Regenerate autoloader
npm run build                 # Rebuild frontend assets
php artisan migrate:fresh --seed  # Reset database
```

---

**Note**: Always run tests (`./scripts/pre-push.sh`) before committing code!