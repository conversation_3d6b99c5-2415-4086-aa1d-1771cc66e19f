# Technology Stack

## Backend
- **Framework**: <PERSON><PERSON> 12
- **PHP Version**: 8.4
- **Web Server**: FrankenP<PERSON> (modern PHP application server)
- **Database**: MySQL 8.0
- **Cache**: Redis
- **Authentication**: Laravel Sanctum
- **Queue System**: Laravel Queue
- **Testing**: Pest PHP

## Frontend  
- **Framework**: Vue.js 3 with Composition API
- **Language**: TypeScript
- **Build Tool**: Vite
- **Styling**: TailwindCSS 4.x
- **UI Components**: reka-ui Vue
- **Icons**: Lucide Vue Next
- **State Management**: Pinia
- **Forms**: vee-validate with Zod schemas
- **Tables**: TanStack Vue Table
- **SPA**: Inertia.js for seamless navigation
- **Routes**: Ziggy.js for Laravel routes in JS

## Development Tools
- **Linting**: ESLint with Vue and TypeScript plugins
- **Formatting**: Prettier with import organization
- **Type Checking**: Vue TSC
- **PHP Code Style**: <PERSON><PERSON> Pint
- **Containerization**: Docker Compose
- **Hot Reload**: Vite HMR

## Key Dependencies
- Laravel packages: inertiajs/inertia-laravel, tightenco/ziggy, maatwebsite/excel
- Vue ecosystem: @inertiajs/vue3, @vueuse/core, vue-sonner
- Validation: @vee-validate/zod, zod
- Utilities: class-variance-authority, clsx, tailwind-merge
