# Testing Standards and Patterns

## Testing Framework: Pest PHP
The project uses Pest PHP for elegant testing with Laravel integration.

## Test Structure (MANDATORY)
```php
describe('ControllerName', function () {
    beforeEach(function () {
        $this->user = User::factory()->create();
    });

    describe('method_name', function () {
        it('describes what it tests', function () {
            $response = $this->actingAs($this->user)->get('/endpoint');

            $response->assertOk();
            $response->assertInertia(
                fn($page) => $page->component('ComponentName')
            );
        });
    });
});
```

## Required Test Types
- **Feature Tests**: Complete user workflows and API endpoints
- **Unit Tests**: Individual functions and methods
- **Component Tests**: Vue component behavior (when needed)
- **Integration Tests**: Service layer and database interactions

## Test Configuration
- **Test Database**: In-memory SQLite for speed
- **Environment**: `APP_ENV=testing`
- **Test Runner**: PHPUnit via Pest
- **Coverage**: Enabled for `app/` directory

## Testing Commands
```bash
php artisan test                    # Run all tests
php artisan test --filter UserTest  # Run specific test
php artisan test --coverage        # Run with coverage
./scripts/pre-push.sh               # Quick validation tests
./scripts/test-local.sh             # Full Docker tests
```

## Test Organization
```
tests/
├── Feature/           # End-to-end functionality tests
│   ├── Auth/         # Authentication tests
│   ├── Settings/     # Settings management
│   └── ...           # Feature-specific tests
├── Unit/             # Isolated unit tests
├── Pest.php          # Pest configuration
└── TestCase.php      # Base test case
```

## Testing Best Practices
- Use factories for test data generation
- Test both happy path and error scenarios
- Verify authorization and permissions
- Test form validation on both frontend and backend
- Mock external services and APIs
- Use descriptive test names that explain the scenario