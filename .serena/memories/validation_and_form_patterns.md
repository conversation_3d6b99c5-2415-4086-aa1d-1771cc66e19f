# Validation and Form Patterns (MANDATORY)

## Form Implementation Pattern
**Always use vee-validate + Inertia for form submissions**

### Frontend Form Structure
```typescript
// Schema validation with vee-validate + Zod
const formSchema = toTypedSchema(zodSchema);

// Inertia form for submission  
const inertiaForm = useInertiaForm({
  field1: '',
  field2: '',
  // ... other fields
});

// Form submission
const onSubmit = async (values: any) => {
  Object.assign(inertiaForm, values);
  inertiaForm.post(route('api.store'), {
    onSuccess: () => { /* handle success */ },
    onError: (errors) => { /* handle errors */ }
  });
};
```

### Template Structure
```vue
<Form :validation-schema="formSchema" @submit="onSubmit">
  <FormField v-slot="{ componentField }" name="fieldName">
    <FormItem>
      <FormLabel>Label *</FormLabel>
      <FormControl>
        <Input v-bind="componentField" placeholder="Placeholder" />
      </FormControl>
      <FormMessage />
    </FormItem>
  </FormField>
</Form>
```

## Backend Validation
- Define validation rules in FormRequest classes
- Store in `app/Http/Requests/<ModuleName>/`
- Sync validation between frontend and backend
- Use proper namespace organization

## Non-Form API Operations
**Use useApi for field checks and standalone actions**
```typescript
import { useApi } from '@/composables/useApiRequest';

const api = useApi();
const result = await api.get('/api/endpoint', { params });
```

## Component Standards
- **DebouncedInput**: Always use for search and filter inputs
- **DataTable**: Use DataTable.vue and DataPagination.vue for tabular data
- **Select Components**: Never use empty string values in SelectItem (use 'none' or 'all')