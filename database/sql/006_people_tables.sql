-- SwinX Academic Management System - People Tables (Students and Faculty)
-- Generated from Lara<PERSON> migrations

-- Enable foreign key constraints
SET FOREIGN_KEY_CHECKS = 1;

-- Students
CREATE TABLE IF NOT EXISTS students (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    student_id VARCHAR(20) NOT NULL UNIQUE,
    full_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    phone VARCHAR(20) NULL,
    oauth_provider ENUM('google', 'microsoft', 'manual') NOT NULL DEFAULT 'google',
    oauth_provider_id VARCHAR(255) NULL,
    avatar_url VARCHAR(500) NULL,
    date_of_birth DATE NULL,
    gender ENUM('male', 'female', 'other') NULL,
    nationality VARCHAR(100) NOT NULL DEFAULT 'Vietnamese',
    national_id VARCHAR(20) NULL UNIQUE,
    address TEXT NULL,
    campus_id BIGINT UNSIGNED NOT NULL,
    program_id BIGINT UNSIGNED NOT NULL,
    specialization_id BIGINT UNSIGNED NULL,
    curriculum_version_id BIGINT UNSIGNED NOT NULL,
    admission_date DATE NOT NULL,
    expected_graduation_date DATE NULL,
    emergency_contact_name VARCHAR(255) NULL,
    emergency_contact_phone VARCHAR(20) NULL,
    emergency_contact_relationship VARCHAR(100) NULL,
    high_school_name VARCHAR(255) NULL,
    high_school_graduation_year YEAR NULL,
    entrance_exam_score DECIMAL(5,2) NULL,
    admission_notes TEXT NULL,
    `status` ENUM('active', 'inactive', 'suspended', 'graduated') NOT NULL DEFAULT 'active',
    last_login_at TIMESTAMP NULL,
    email_verified_at TIMESTAMP NULL,
    remember_token VARCHAR(100) NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    PRIMARY KEY (id),
    INDEX idx_students_student_id (student_id),
    INDEX idx_students_email (email),
    INDEX idx_students_oauth (oauth_provider, oauth_provider_id),
    INDEX idx_students_campus_id (campus_id),
    INDEX idx_students_program_specialization (program_id, specialization_id),
    INDEX idx_students_status (`status`),
    CONSTRAINT fk_students_campus_id FOREIGN KEY (campus_id) REFERENCES campuses(id) ON DELETE RESTRICT,
    CONSTRAINT fk_students_program_id FOREIGN KEY (program_id) REFERENCES programs(id) ON DELETE RESTRICT,
    CONSTRAINT fk_students_specialization_id FOREIGN KEY (specialization_id) REFERENCES specializations(id) ON DELETE SET NULL,
    CONSTRAINT fk_students_curriculum_version_id FOREIGN KEY (curriculum_version_id) REFERENCES curriculum_versions(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Lectures/Faculty
CREATE TABLE IF NOT EXISTS lectures (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    employee_id VARCHAR(20) NOT NULL UNIQUE,
    title VARCHAR(10) NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    phone VARCHAR(20) NULL,
    mobile_phone VARCHAR(20) NULL,
    campus_id BIGINT UNSIGNED NOT NULL,
    department VARCHAR(100) NULL,
    faculty VARCHAR(100) NULL,
    specialization VARCHAR(255) NULL,
    expertise_areas JSON NULL,
    academic_rank ENUM('lecturer', 'senior_lecturer', 'associate_professor', 'professor', 'emeritus_professor', 'visiting_lecturer', 'adjunct_professor') NOT NULL DEFAULT 'lecturer',
    highest_degree VARCHAR(50) NULL,
    degree_field VARCHAR(255) NULL,
    alma_mater VARCHAR(255) NULL,
    graduation_year YEAR NULL,
    hire_date DATE NOT NULL,
    contract_start_date DATE NULL,
    contract_end_date DATE NULL,
    employment_type ENUM('full_time', 'part_time', 'contract', 'visiting', 'emeritus') NOT NULL DEFAULT 'full_time',
    employment_status ENUM('active', 'on_leave', 'sabbatical', 'retired', 'terminated', 'suspended') NOT NULL DEFAULT 'active',
    preferred_teaching_days JSON NULL,
    preferred_start_time TIME NULL,
    preferred_end_time TIME NULL,
    max_teaching_hours_per_week INT NOT NULL DEFAULT 40,
    teaching_modalities JSON NULL,
    office_address TEXT NULL,
    office_phone VARCHAR(20) NULL,
    emergency_contact_name TEXT NULL,
    emergency_contact_phone VARCHAR(20) NULL,
    emergency_contact_relationship VARCHAR(50) NULL,
    biography TEXT NULL,
    certifications JSON NULL,
    languages JSON NULL,
    hourly_rate DECIMAL(10,2) NULL,
    salary DECIMAL(12,2) NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    can_teach_online BOOLEAN NOT NULL DEFAULT TRUE,
    is_available_for_assignment BOOLEAN NOT NULL DEFAULT TRUE,
    notes TEXT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    PRIMARY KEY (id),
    INDEX idx_lectures_campus_active (campus_id, is_active),
    INDEX idx_lectures_employment_active (employment_status, is_active),
    INDEX idx_lectures_rank_specialization (academic_rank, specialization),
    INDEX idx_lectures_hire_date (hire_date),
    INDEX idx_lectures_email (email),
    INDEX idx_lectures_employee_id (employee_id),
    INDEX idx_lectures_name (last_name, first_name),
    INDEX idx_lectures_department (department, faculty),
    INDEX idx_lectures_available (is_available_for_assignment, employment_status),
    CONSTRAINT fk_lectures_campus_id FOREIGN KEY (campus_id) REFERENCES campuses(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Academic holds
CREATE TABLE IF NOT EXISTS academic_holds (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    student_id BIGINT UNSIGNED NOT NULL,
    hold_type ENUM('financial', 'academic', 'disciplinary', 'administrative', 'health', 'library') NOT NULL,
    hold_category ENUM('registration', 'graduation', 'transcript', 'all') NOT NULL DEFAULT 'registration',
    title VARCHAR(255) NOT NULL,
    description TEXT NULL,
    amount DECIMAL(10,2) NULL,
    priority ENUM('high', 'medium', 'low') NOT NULL DEFAULT 'medium',
    status ENUM('active', 'resolved', 'waived', 'expired') NOT NULL DEFAULT 'active',
    placed_date DATE NOT NULL,
    due_date DATE NULL,
    resolved_date DATE NULL,
    placed_by_user_id BIGINT UNSIGNED NULL,
    resolved_by_user_id BIGINT UNSIGNED NULL,
    resolution_notes TEXT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    PRIMARY KEY (id),
    INDEX idx_academic_holds_student (student_id),
    INDEX idx_academic_holds_type_status (hold_type, status),
    INDEX idx_academic_holds_category (hold_category),
    CONSTRAINT fk_academic_holds_student_id FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    CONSTRAINT fk_academic_holds_placed_by_user_id FOREIGN KEY (placed_by_user_id) REFERENCES users(id) ON DELETE SET NULL,
    CONSTRAINT fk_academic_holds_resolved_by_user_id FOREIGN KEY (resolved_by_user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
