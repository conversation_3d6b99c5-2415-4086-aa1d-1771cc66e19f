-- SwinX Academic Management System - Course Delivery Tables
-- Generated from Laravel migrations

-- Enable foreign key constraints
SET FOREIGN_KEY_CHECKS = 1;

-- Course offerings
CREATE TABLE IF NOT EXISTS course_offerings (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    semester_id BIGINT UNSIGNED NOT NULL,
    unit_id BIGINT UNSIGNED NOT NULL,
    lecture_id BIGINT UNSIGNED NULL,
    section_code VARCHAR(10) NULL,
    max_capacity INT NOT NULL DEFAULT 1000,
    current_enrollment INT NOT NULL DEFAULT 0,
    waitlist_capacity INT NOT NULL DEFAULT 10,
    current_waitlist INT NOT NULL DEFAULT 0,
    delivery_mode ENUM('in_person', 'online', 'hybrid', 'blended') NOT NULL DEFAULT 'in_person',
    schedule_days JSON NULL,
    schedule_time_start TIME NULL,
    schedule_time_end TIME NULL,
    location VARCHAR(255) NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    enrollment_status ENUM('open', 'closed', 'waitlist_only', 'cancelled') NOT NULL DEFAULT 'open',
    registration_start_date DATE NULL,
    registration_end_date DATE NULL,
    special_requirements TEXT NULL,
    notes TEXT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    PRIMARY KEY (id),
    INDEX idx_course_offerings_semester_unit (semester_id, unit_id),
    INDEX idx_course_offerings_lecture_semester (lecture_id, semester_id),
    INDEX idx_course_offerings_active_status (is_active, enrollment_status),
    INDEX idx_course_offerings_delivery (delivery_mode),
    INDEX idx_course_offerings_enrollment (current_enrollment, max_capacity),
    INDEX idx_course_offerings_registration (registration_start_date, registration_end_date),
    UNIQUE KEY idx_course_offerings_unique (semester_id, unit_id, section_code),
    CONSTRAINT fk_course_offerings_semester_id FOREIGN KEY (semester_id) REFERENCES semesters(id) ON DELETE CASCADE,
    CONSTRAINT fk_course_offerings_unit_id FOREIGN KEY (unit_id) REFERENCES units(id) ON DELETE CASCADE,
    CONSTRAINT fk_course_offerings_lecture_id FOREIGN KEY (lecture_id) REFERENCES lectures(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Course registrations
CREATE TABLE IF NOT EXISTS course_registrations (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    student_id BIGINT UNSIGNED NOT NULL,
    course_offering_id BIGINT UNSIGNED NOT NULL,
    semester_id BIGINT UNSIGNED NOT NULL,
    registration_status ENUM('registered', 'confirmed', 'dropped', 'withdrawn', 'completed') NOT NULL DEFAULT 'registered',
    registration_date TIMESTAMP NOT NULL,
    registration_method ENUM('online', 'advisor', 'admin_override') NOT NULL DEFAULT 'online',
    credit_hours DECIMAL(4,2) NOT NULL,
    final_grade VARCHAR(3) NULL,
    grade_points DECIMAL(3,2) NULL,
    attempt_number INT NOT NULL DEFAULT 1,
    is_retake BOOLEAN NOT NULL DEFAULT FALSE,
    drop_date TIMESTAMP NULL,
    withdrawal_date TIMESTAMP NULL,
    completion_date TIMESTAMP NULL,
    retake_fee DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    is_retake_paid ENUM('yes', 'no') NOT NULL DEFAULT 'no',
    notes TEXT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    PRIMARY KEY (id),
    UNIQUE KEY idx_course_registrations_unique (student_id, course_offering_id, semester_id),
    INDEX idx_course_registrations_student (student_id),
    INDEX idx_course_registrations_offering (course_offering_id),
    INDEX idx_course_registrations_semester (semester_id),
    INDEX idx_course_registrations_status (registration_status),
    CONSTRAINT fk_course_registrations_student_id FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    CONSTRAINT fk_course_registrations_course_offering_id FOREIGN KEY (course_offering_id) REFERENCES course_offerings(id) ON DELETE CASCADE,
    CONSTRAINT fk_course_registrations_semester_id FOREIGN KEY (semester_id) REFERENCES semesters(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Class sessions
CREATE TABLE IF NOT EXISTS class_sessions (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    course_offering_id BIGINT UNSIGNED NOT NULL,
    room_id BIGINT UNSIGNED NULL,
    room_booking_id BIGINT UNSIGNED NULL,
    instructor_id BIGINT UNSIGNED NULL,
    session_title VARCHAR(200) NULL,
    session_description TEXT NULL,
    session_date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    duration_minutes INT NULL,
    session_type ENUM('lecture', 'tutorial', 'practical', 'laboratory', 'seminar', 'workshop', 'exam', 'assessment', 'field_trip', 'guest_lecture', 'review', 'other') NOT NULL DEFAULT 'lecture',
    delivery_mode ENUM('in_person', 'online', 'hybrid', 'blended') NOT NULL DEFAULT 'in_person',
    status ENUM('scheduled', 'in_progress', 'completed', 'cancelled', 'postponed', 'moved') NOT NULL DEFAULT 'scheduled',
    online_meeting_url VARCHAR(500) NULL,
    meeting_id VARCHAR(100) NULL,
    meeting_password VARCHAR(100) NULL,
    learning_objectives JSON NULL,
    required_materials JSON NULL,
    topics_covered JSON NULL,
    attendance_required BOOLEAN NOT NULL DEFAULT TRUE,
    attendance_tracking_enabled BOOLEAN NOT NULL DEFAULT TRUE,
    expected_attendees INT NULL,
    actual_attendees INT NULL,
    attendance_percentage DECIMAL(5,2) NULL,
    is_assessment BOOLEAN NOT NULL DEFAULT FALSE,
    assessment_weight DECIMAL(5,2) NULL,
    assessment_duration_minutes INT NULL,
    assessment_materials_allowed JSON NULL,
    is_recurring BOOLEAN NOT NULL DEFAULT FALSE,
    parent_session_id BIGINT UNSIGNED NULL,
    sequence_number INT NULL,
    instructor_notes TEXT NULL,
    admin_notes TEXT NULL,
    student_instructions TEXT NULL,
    cancellation_reason TEXT NULL,
    scheduled_at TIMESTAMP NULL,
    started_at TIMESTAMP NULL,
    ended_at TIMESTAMP NULL,
    cancelled_at TIMESTAMP NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    PRIMARY KEY (id),
    INDEX idx_class_sessions_co_schedule (course_offering_id, session_date, start_time),
    INDEX idx_class_sessions_room_conflict (room_id, session_date, start_time, end_time),
    INDEX idx_class_sessions_instructor_schedule (instructor_id, session_date, start_time),
    INDEX idx_class_sessions_type_status (session_type, status),
    INDEX idx_class_sessions_delivery_date (delivery_mode, session_date),
    INDEX idx_class_sessions_assessment (is_assessment, session_date),
    INDEX idx_class_sessions_attendance_config (attendance_required, attendance_tracking_enabled),
    INDEX idx_class_sessions_recurring (is_recurring, parent_session_id),
    INDEX idx_class_sessions_sequence (sequence_number),
    INDEX idx_class_sessions_daily_room_schedule (session_date, room_id, start_time, end_time),
    INDEX idx_class_sessions_instructor_daily_schedule (instructor_id, session_date, session_type),
    UNIQUE KEY idx_class_sessions_co_sequence (course_offering_id, sequence_number),
    CONSTRAINT fk_class_sessions_course_offering_id FOREIGN KEY (course_offering_id) REFERENCES course_offerings(id) ON DELETE CASCADE,
    CONSTRAINT fk_class_sessions_room_id FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE SET NULL,
    CONSTRAINT fk_class_sessions_room_booking_id FOREIGN KEY (room_booking_id) REFERENCES room_bookings(id) ON DELETE SET NULL,
    CONSTRAINT fk_class_sessions_instructor_id FOREIGN KEY (instructor_id) REFERENCES lectures(id) ON DELETE SET NULL,
    CONSTRAINT fk_class_sessions_parent_session_id FOREIGN KEY (parent_session_id) REFERENCES class_sessions(id) ON DELETE SET NULL,
    CONSTRAINT check_sessions_times CHECK (start_time < end_time),
    CONSTRAINT check_sessions_expected_attendees_positive CHECK (expected_attendees IS NULL OR expected_attendees > 0),
    CONSTRAINT check_sessions_actual_attendees_valid CHECK (actual_attendees IS NULL OR actual_attendees >= 0),
    CONSTRAINT check_sessions_attendance_percentage CHECK (attendance_percentage IS NULL OR (attendance_percentage >= 0 AND attendance_percentage <= 100)),
    CONSTRAINT check_sessions_assessment_weight CHECK (assessment_weight IS NULL OR (assessment_weight >= 0 AND assessment_weight <= 100))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add full-text search for class sessions
ALTER TABLE class_sessions ADD FULLTEXT(session_title, session_description, student_instructions);

-- Attendances
CREATE TABLE IF NOT EXISTS attendances (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    class_session_id BIGINT UNSIGNED NOT NULL,
    student_id BIGINT UNSIGNED NOT NULL,
    recorded_by_lecture_id BIGINT UNSIGNED NULL,
    status ENUM('present', 'absent', 'late', 'excused', 'partial', 'medical_leave', 'official_leave') NOT NULL DEFAULT 'absent',
    check_in_time TIMESTAMP NULL,
    check_out_time TIMESTAMP NULL,
    minutes_late INT NOT NULL DEFAULT 0,
    minutes_present INT NULL,
    recording_method ENUM('manual', 'qr_code', 'rfid', 'biometric', 'mobile_app', 'online_participation', 'auto_system') NOT NULL DEFAULT 'manual',
    notes TEXT NULL,
    excuse_reason TEXT NULL,
    excuse_document_path VARCHAR(500) NULL,
    participation_level ENUM('excellent', 'good', 'average', 'poor', 'none') NULL,
    participation_score DECIMAL(3,1) NULL,
    participation_notes TEXT NULL,
    is_verified BOOLEAN NOT NULL DEFAULT FALSE,
    affects_grade BOOLEAN NOT NULL DEFAULT TRUE,
    is_makeup_allowed BOOLEAN NOT NULL DEFAULT FALSE,
    verified_at TIMESTAMP NULL,
    verified_by_lecture_id BIGINT UNSIGNED NULL,
    batch_id VARCHAR(50) NULL,
    device_info JSON NULL,
    ip_address VARCHAR(45) NULL,
    latitude DECIMAL(10,8) NULL,
    longitude DECIMAL(11,8) NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    PRIMARY KEY (id),
    INDEX idx_attendances_session_status (class_session_id, status),
    INDEX idx_attendances_student_status (student_id, status),
    INDEX idx_attendances_session_student (class_session_id, student_id),
    INDEX idx_attendances_recorded_by (recorded_by_lecture_id, created_at),
    INDEX idx_attendances_method (recording_method, created_at),
    INDEX idx_attendances_grade (status, affects_grade),
    INDEX idx_attendances_verification (is_verified, verified_at),
    INDEX idx_attendances_batch (batch_id),
    INDEX idx_attendances_times (check_in_time, check_out_time),
    INDEX idx_attendances_student_grade (student_id, status, affects_grade, created_at),
    INDEX idx_attendances_student_timeline (student_id, check_in_time, status),
    INDEX idx_attendances_recorded_by_lecture (recorded_by_lecture_id, created_at),
    UNIQUE KEY idx_attendances_unique (class_session_id, student_id),
    CONSTRAINT fk_attendances_class_session_id FOREIGN KEY (class_session_id) REFERENCES class_sessions(id) ON DELETE CASCADE,
    CONSTRAINT fk_attendances_student_id FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    CONSTRAINT fk_attendances_recorded_by_lecture_id FOREIGN KEY (recorded_by_lecture_id) REFERENCES lectures(id) ON DELETE SET NULL,
    CONSTRAINT fk_attendances_verified_by_lecture_id FOREIGN KEY (verified_by_lecture_id) REFERENCES lectures(id) ON DELETE SET NULL,
    CONSTRAINT check_attendances_minutes_late_positive CHECK (minutes_late >= 0),
    CONSTRAINT check_attendances_minutes_present_positive CHECK (minutes_present IS NULL OR minutes_present >= 0),
    CONSTRAINT check_attendances_participation_score CHECK (participation_score IS NULL OR (participation_score >= 0 AND participation_score <= 10))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
