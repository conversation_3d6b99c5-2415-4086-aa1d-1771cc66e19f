-- SwinX Academic Management System - Assessment and Academic Records Tables
-- Generated from Laravel migrations

-- Enable foreign key constraints
SET FOREIGN_KEY_CHECKS = 1;

-- Syllabus
CREATE TABLE IF NOT EXISTS syllabus (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    unit_id BIGINT UNSIGNED NOT NULL,
    version VARCHAR(30) NULL,
    description TEXT NULL,
    total_hours INT NULL,
    hours_per_session INT NULL,
    semester_id BIGINT UNSIGNED NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    PRIMARY KEY (id),
    UNIQUE KEY idx_syllabus_unit_semester_active (unit_id, semester_id, is_active),
    CONSTRAINT fk_syllabus_unit_id FOREIGN KEY (unit_id) REFERENCES units(id) ON DELETE CASCADE,
    CONSTRAINT fk_syllabus_semester_id FOREIGN KEY (semester_id) REFERENCES semesters(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Assessment components
CREATE TABLE IF NOT EXISTS assessment_components (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    syllabus_id BIGINT UNSIGNED NOT NULL,
    name VARCHAR(100) NULL,
    code VARCHAR(20) NULL,
    description TEXT NULL,
    weight DECIMAL(5,2) NULL,
    type ENUM('quiz', 'assignment', 'project', 'exam', 'online_activity', 'other') NOT NULL,
    is_required_to_sit_final_exam BOOLEAN NOT NULL DEFAULT TRUE,
    due_date DATETIME NULL,
    available_from DATETIME NULL,
    late_submission_deadline DATETIME NULL,
    late_penalty_percentage DECIMAL(5,2) NOT NULL DEFAULT 0.00,
    late_penalty_type ENUM('per_day', 'per_hour', 'fixed', 'none') NOT NULL DEFAULT 'none',
    submission_type ENUM('online', 'in_person', 'both', 'no_submission') NOT NULL DEFAULT 'online',
    allowed_file_types JSON NULL,
    max_file_size_mb INT NULL,
    max_submissions INT NOT NULL DEFAULT 1,
    allow_resubmission BOOLEAN NOT NULL DEFAULT FALSE,
    is_group_work BOOLEAN NOT NULL DEFAULT FALSE,
    min_group_size INT NULL,
    max_group_size INT NULL,
    students_form_groups BOOLEAN NOT NULL DEFAULT TRUE,
    assessment_criteria JSON NULL,
    grading_instructions TEXT NULL,
    is_published BOOLEAN NOT NULL DEFAULT FALSE,
    scores_published BOOLEAN NOT NULL DEFAULT FALSE,
    is_extra_credit BOOLEAN NOT NULL DEFAULT FALSE,
    status ENUM('draft', 'published', 'in_progress', 'grading', 'completed', 'cancelled') NOT NULL DEFAULT 'draft',
    sort_order INT NOT NULL DEFAULT 0,
    category VARCHAR(50) NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    PRIMARY KEY (id),
    UNIQUE KEY idx_assessment_components_syllabus_code (syllabus_id, code),
    INDEX idx_assessment_components_syllabus_type (syllabus_id, type),
    INDEX idx_assessment_components_syllabus_order (syllabus_id, sort_order),
    INDEX idx_assessment_components_due_status (due_date, status),
    INDEX idx_assessment_components_published_status (is_published, status),
    INDEX idx_assessment_components_group_type (is_group_work, type),
    INDEX idx_assessment_components_category_order (category, sort_order),
    CONSTRAINT fk_assessment_components_syllabus_id FOREIGN KEY (syllabus_id) REFERENCES syllabus(id) ON DELETE CASCADE,
    CONSTRAINT check_weight_percentage CHECK (weight >= 0 AND weight <= 100),
    CONSTRAINT check_late_penalty_percentage CHECK (late_penalty_percentage >= 0 AND late_penalty_percentage <= 100),
    CONSTRAINT check_file_size_positive CHECK (max_file_size_mb IS NULL OR max_file_size_mb > 0),
    CONSTRAINT check_max_submissions_positive CHECK (max_submissions >= 1),
    CONSTRAINT check_group_size_valid CHECK (
        (min_group_size IS NULL OR min_group_size >= 1) AND
        (max_group_size IS NULL OR max_group_size >= 1) AND
        (min_group_size IS NULL OR max_group_size IS NULL OR min_group_size <= max_group_size)
    )
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add full-text search for assessment components
ALTER TABLE assessment_components ADD FULLTEXT(name, description, grading_instructions);

-- Assessment component details
CREATE TABLE IF NOT EXISTS assessment_component_details (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    component_id BIGINT UNSIGNED NOT NULL,
    name VARCHAR(100) NOT NULL,
    weight DECIMAL(5,2) NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    PRIMARY KEY (id),
    CONSTRAINT fk_assessment_component_details_component_id FOREIGN KEY (component_id) REFERENCES assessment_components(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Student groups (for group assignments)
CREATE TABLE IF NOT EXISTS student_groups (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT NULL,
    assessment_component_id BIGINT UNSIGNED NOT NULL,
    course_offering_id BIGINT UNSIGNED NOT NULL,
    created_by_student_id BIGINT UNSIGNED NULL,
    approved_by_lecture_id BIGINT UNSIGNED NULL,
    min_members INT NOT NULL DEFAULT 1,
    max_members INT NOT NULL DEFAULT 5,
    current_members INT NOT NULL DEFAULT 0,
    status ENUM('forming', 'complete', 'approved', 'rejected') NOT NULL DEFAULT 'forming',
    notes TEXT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    PRIMARY KEY (id),
    INDEX idx_student_groups_component (assessment_component_id),
    INDEX idx_student_groups_offering (course_offering_id),
    INDEX idx_student_groups_status (status),
    CONSTRAINT fk_student_groups_assessment_component_id FOREIGN KEY (assessment_component_id) REFERENCES assessment_components(id) ON DELETE CASCADE,
    CONSTRAINT fk_student_groups_course_offering_id FOREIGN KEY (course_offering_id) REFERENCES course_offerings(id) ON DELETE CASCADE,
    CONSTRAINT fk_student_groups_created_by_student_id FOREIGN KEY (created_by_student_id) REFERENCES students(id) ON DELETE SET NULL,
    CONSTRAINT fk_student_groups_approved_by_lecture_id FOREIGN KEY (approved_by_lecture_id) REFERENCES lectures(id) ON DELETE SET NULL,
    CONSTRAINT check_group_member_counts CHECK (
        min_members >= 1 AND
        max_members >= 1 AND
        min_members <= max_members AND
        current_members >= 0 AND
        current_members <= max_members
    )
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Assessment component detail scores
CREATE TABLE IF NOT EXISTS assessment_component_detail_scores (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    assessment_component_detail_id BIGINT UNSIGNED NOT NULL,
    student_id BIGINT UNSIGNED NOT NULL,
    course_offering_id BIGINT UNSIGNED NOT NULL,
    graded_by_lecture_id BIGINT UNSIGNED NULL,
    points_earned DECIMAL(8,2) NULL,
    percentage_score DECIMAL(5,2) NULL,
    letter_grade VARCHAR(5) NULL,
    gpa_points DECIMAL(3,2) NULL,
    submitted_at TIMESTAMP NULL,
    graded_at TIMESTAMP NULL,
    submission_attempt INT NOT NULL DEFAULT 1,
    submission_files JSON NULL,
    submission_text LONGTEXT NULL,
    submission_url VARCHAR(500) NULL,
    is_late BOOLEAN NOT NULL DEFAULT FALSE,
    minutes_late INT NOT NULL DEFAULT 0,
    late_penalty_applied DECIMAL(5,2) NOT NULL DEFAULT 0.00,
    late_excuse TEXT NULL,
    late_excuse_approved BOOLEAN NOT NULL DEFAULT FALSE,
    status ENUM('not_submitted', 'submitted', 'grading', 'graded', 'returned', 'resubmit_required', 'excused', 'incomplete', 'cancelled') NOT NULL DEFAULT 'not_submitted',
    score_status ENUM('draft', 'provisional', 'final', 'disputed', 'under_review') NOT NULL DEFAULT 'draft',
    instructor_feedback LONGTEXT NULL,
    private_notes LONGTEXT NULL,
    rubric_scores JSON NULL,
    bonus_points DECIMAL(8,2) NOT NULL DEFAULT 0.00,
    bonus_reason TEXT NULL,
    student_group_id BIGINT UNSIGNED NULL,
    individual_score_override BOOLEAN NOT NULL DEFAULT FALSE,
    individual_override_reason TEXT NULL,
    plagiarism_suspected BOOLEAN NOT NULL DEFAULT FALSE,
    plagiarism_score DECIMAL(5,2) NULL,
    plagiarism_notes TEXT NULL,
    integrity_status ENUM('clear', 'under_investigation', 'violation_confirmed', 'violation_minor', 'violation_major') NOT NULL DEFAULT 'clear',
    score_history JSON NULL,
    last_modified_at TIMESTAMP NULL,
    last_modified_by_lecture_id BIGINT UNSIGNED NULL,
    is_extra_credit BOOLEAN NOT NULL DEFAULT FALSE,
    is_makeup BOOLEAN NOT NULL DEFAULT FALSE,
    special_circumstances TEXT NULL,
    score_excluded BOOLEAN NOT NULL DEFAULT FALSE,
    exclusion_reason TEXT NULL,
    student_comments TEXT NULL,
    appeal_requested BOOLEAN NOT NULL DEFAULT FALSE,
    appeal_requested_at TIMESTAMP NULL,
    appeal_reason TEXT NULL,
    appeal_status ENUM('none', 'pending', 'under_review', 'approved', 'denied') NOT NULL DEFAULT 'none',
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    PRIMARY KEY (id),
    INDEX idx_detail_scores_detail_student (assessment_component_detail_id, student_id),
    INDEX idx_detail_scores_student_status (student_id, status),
    INDEX idx_detail_scores_student_score_status (student_id, score_status),
    INDEX idx_detail_scores_course_status (course_offering_id, status),
    INDEX idx_detail_scores_graded_by_date (graded_by_lecture_id, graded_at),
    INDEX idx_detail_scores_submitted_status (submitted_at, status),
    INDEX idx_detail_scores_late_penalty (is_late, late_penalty_applied),
    INDEX idx_detail_scores_plagiarism_integrity (plagiarism_suspected, integrity_status),
    INDEX idx_detail_scores_appeal_status (appeal_requested, appeal_status),
    INDEX idx_detail_scores_group_override (student_group_id, individual_score_override),
    INDEX idx_detail_scores_excluded_extra (score_excluded, is_extra_credit),
    INDEX idx_detail_scores_student_gpa_calc (student_id, score_status, score_excluded, gpa_points),
    INDEX idx_detail_scores_student_transcript (student_id, graded_at, score_status),
    INDEX idx_detail_scores_student_score_timeline (student_id, graded_at, score_status, points_earned),
    INDEX idx_detail_scores_graded_by_lecture (graded_by_lecture_id, graded_at),
    UNIQUE KEY idx_detail_scores_unique (assessment_component_detail_id, student_id, course_offering_id, submission_attempt),
    CONSTRAINT fk_detail_scores_detail_id FOREIGN KEY (assessment_component_detail_id) REFERENCES assessment_component_details(id) ON DELETE CASCADE,
    CONSTRAINT fk_detail_scores_student_id FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    CONSTRAINT fk_detail_scores_course_offering_id FOREIGN KEY (course_offering_id) REFERENCES course_offerings(id) ON DELETE CASCADE,
    CONSTRAINT fk_detail_scores_graded_by FOREIGN KEY (graded_by_lecture_id) REFERENCES lectures(id) ON DELETE SET NULL,
    CONSTRAINT fk_detail_scores_modified_by FOREIGN KEY (last_modified_by_lecture_id) REFERENCES lectures(id) ON DELETE SET NULL,
    CONSTRAINT fk_detail_scores_student_group_id FOREIGN KEY (student_group_id) REFERENCES student_groups(id) ON DELETE SET NULL,
    CONSTRAINT check_scores_percentage_score CHECK (percentage_score IS NULL OR (percentage_score >= 0 AND percentage_score <= 100)),
    CONSTRAINT check_scores_gpa_points CHECK (gpa_points IS NULL OR (gpa_points >= 0 AND gpa_points <= 4)),
    CONSTRAINT check_scores_submission_attempt_positive CHECK (submission_attempt >= 1),
    CONSTRAINT check_scores_minutes_late_positive CHECK (minutes_late >= 0),
    CONSTRAINT check_scores_late_penalty_applied CHECK (late_penalty_applied >= 0 AND late_penalty_applied <= 100),
    CONSTRAINT check_scores_plagiarism_score CHECK (plagiarism_score IS NULL OR (plagiarism_score >= 0 AND plagiarism_score <= 100))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Academic records
CREATE TABLE IF NOT EXISTS academic_records (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    student_id BIGINT UNSIGNED NOT NULL,
    course_offering_id BIGINT UNSIGNED NOT NULL,
    semester_id BIGINT UNSIGNED NOT NULL,
    unit_id BIGINT UNSIGNED NOT NULL,
    program_id BIGINT UNSIGNED NOT NULL,
    campus_id BIGINT UNSIGNED NOT NULL,
    final_percentage DECIMAL(5,2) NULL,
    final_letter_grade VARCHAR(5) NULL,
    grade_points DECIMAL(3,2) NULL,
    quality_points DECIMAL(6,2) NULL,
    credit_hours DECIMAL(4,2) NOT NULL,
    credit_hours_earned DECIMAL(4,2) NOT NULL DEFAULT 0.00,
    grade_status ENUM('in_progress', 'provisional', 'final', 'incomplete', 'withdrawn', 'failed', 'pass_no_credit', 'audit', 'transfer_credit') NOT NULL DEFAULT 'in_progress',
    completion_status ENUM('enrolled', 'completed', 'withdrawn', 'failed', 'incomplete', 'in_progress') NOT NULL DEFAULT 'enrolled',
    enrollment_date DATE NOT NULL,
    completion_date DATE NULL,
    grade_submission_date DATE NULL,
    grade_finalized_date DATE NULL,
    attendance_percentage DECIMAL(5,2) NULL,
    total_absences INT NOT NULL DEFAULT 0,
    total_class_sessions INT NULL,
    meets_attendance_requirement BOOLEAN NOT NULL DEFAULT TRUE,
    is_repeat_course BOOLEAN NOT NULL DEFAULT FALSE,
    attempt_number INT NOT NULL DEFAULT 1,
    original_record_id BIGINT UNSIGNED NULL,
    is_transfer_credit BOOLEAN NOT NULL DEFAULT FALSE,
    transfer_institution VARCHAR(200) NULL,
    transfer_course_code VARCHAR(50) NULL,
    transfer_course_title VARCHAR(200) NULL,
    is_advanced_placement BOOLEAN NOT NULL DEFAULT FALSE,
    is_challenge_exam BOOLEAN NOT NULL DEFAULT FALSE,
    is_credit_by_exam BOOLEAN NOT NULL DEFAULT FALSE,
    grade_breakdown JSON NULL,
    raw_percentage DECIMAL(5,2) NULL,
    curve_adjustment DECIMAL(5,2) NOT NULL DEFAULT 0.00,
    grade_adjustment_reason TEXT NULL,
    excluded_from_gpa BOOLEAN NOT NULL DEFAULT FALSE,
    gpa_exclusion_reason TEXT NULL,
    instructor_comments TEXT NULL,
    administrative_notes TEXT NULL,
    instructor_id BIGINT UNSIGNED NULL,
    grade_submitted_by_lecture_id BIGINT UNSIGNED NULL,
    grade_approved_by_lecture_id BIGINT UNSIGNED NULL,
    affects_academic_standing BOOLEAN NOT NULL DEFAULT TRUE,
    affects_graduation_requirement BOOLEAN NOT NULL DEFAULT TRUE,
    satisfies_prerequisite BOOLEAN NOT NULL DEFAULT TRUE,
    grade_history JSON NULL,
    last_grade_change_at TIMESTAMP NULL,
    last_changed_by_lecture_id BIGINT UNSIGNED NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    PRIMARY KEY (id),
    UNIQUE KEY idx_academic_records_unique (student_id, course_offering_id),
    INDEX idx_academic_records_student_semester (student_id, semester_id),
    INDEX idx_academic_records_student_grade_completion (student_id, grade_status, completion_status),
    INDEX idx_academic_records_student_gpa (student_id, excluded_from_gpa, grade_points),
    INDEX idx_academic_records_unit_semester_grade (unit_id, semester_id, grade_status),
    INDEX idx_academic_records_program_semester_completion (program_id, semester_id, completion_status),
    INDEX idx_academic_records_campus_semester_grade (campus_id, semester_id, grade_status),
    INDEX idx_academic_records_instructor_semester_grade (instructor_id, semester_id, grade_status),
    INDEX idx_academic_records_repeat_attempt (is_repeat_course, attempt_number),
    INDEX idx_academic_records_transfer_grade (is_transfer_credit, grade_status),
    INDEX idx_academic_records_finalized_date (grade_finalized_date, grade_status),
    INDEX idx_academic_records_enrollment_completion (enrollment_date, completion_date),
    INDEX idx_academic_records_transcript (student_id, completion_status, grade_finalized_date, credit_hours),
    INDEX idx_academic_records_gpa_calc (student_id, excluded_from_gpa, quality_points, credit_hours),
    INDEX idx_academic_records_student_final_grades (student_id, grade_finalized_date, final_letter_grade),
    INDEX idx_academic_records_semester_unit_completion (semester_id, unit_id, completion_status),
    CONSTRAINT fk_academic_records_student_id FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    CONSTRAINT fk_academic_records_course_offering_id FOREIGN KEY (course_offering_id) REFERENCES course_offerings(id) ON DELETE CASCADE,
    CONSTRAINT fk_academic_records_semester_id FOREIGN KEY (semester_id) REFERENCES semesters(id) ON DELETE RESTRICT,
    CONSTRAINT fk_academic_records_unit_id FOREIGN KEY (unit_id) REFERENCES units(id) ON DELETE RESTRICT,
    CONSTRAINT fk_academic_records_program_id FOREIGN KEY (program_id) REFERENCES programs(id) ON DELETE RESTRICT,
    CONSTRAINT fk_academic_records_campus_id FOREIGN KEY (campus_id) REFERENCES campuses(id) ON DELETE RESTRICT,
    CONSTRAINT fk_academic_records_original_record_id FOREIGN KEY (original_record_id) REFERENCES academic_records(id) ON DELETE SET NULL,
    CONSTRAINT fk_academic_records_instructor_id FOREIGN KEY (instructor_id) REFERENCES lectures(id) ON DELETE SET NULL,
    CONSTRAINT fk_academic_records_grade_submitted_by FOREIGN KEY (grade_submitted_by_lecture_id) REFERENCES lectures(id) ON DELETE SET NULL,
    CONSTRAINT fk_academic_records_grade_approved_by FOREIGN KEY (grade_approved_by_lecture_id) REFERENCES lectures(id) ON DELETE SET NULL,
    CONSTRAINT fk_academic_records_last_changed_by FOREIGN KEY (last_changed_by_lecture_id) REFERENCES lectures(id) ON DELETE SET NULL,
    CONSTRAINT check_records_final_percentage CHECK (final_percentage IS NULL OR (final_percentage >= 0 AND final_percentage <= 100)),
    CONSTRAINT check_records_grade_points CHECK (grade_points IS NULL OR (grade_points >= 0 AND grade_points <= 4)),
    CONSTRAINT check_records_credit_hours_positive CHECK (credit_hours > 0),
    CONSTRAINT check_records_credit_hours_earned CHECK (credit_hours_earned >= 0 AND credit_hours_earned <= credit_hours),
    CONSTRAINT check_records_quality_points CHECK (quality_points IS NULL OR quality_points >= 0),
    CONSTRAINT check_records_attendance_percentage CHECK (attendance_percentage IS NULL OR (attendance_percentage >= 0 AND attendance_percentage <= 100)),
    CONSTRAINT check_records_attempt_number_positive CHECK (attempt_number >= 1),
    CONSTRAINT check_records_curve_adjustment CHECK (curve_adjustment >= -100 AND curve_adjustment <= 100)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- GPA calculations
CREATE TABLE IF NOT EXISTS gpa_calculations (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    student_id BIGINT UNSIGNED NOT NULL,
    semester_id BIGINT UNSIGNED NULL,
    program_id BIGINT UNSIGNED NULL,
    calculation_type ENUM('semester', 'cumulative', 'major', 'program', 'year', 'transfer', 'institutional') NOT NULL DEFAULT 'semester',
    gpa DECIMAL(4,3) NULL,
    quality_points DECIMAL(8,3) NOT NULL DEFAULT 0.000,
    credit_hours_attempted DECIMAL(6,2) NOT NULL DEFAULT 0.00,
    credit_hours_earned DECIMAL(6,2) NOT NULL DEFAULT 0.00,
    credit_hours_gpa DECIMAL(6,2) NOT NULL DEFAULT 0.00,
    total_courses INT NOT NULL DEFAULT 0,
    completed_courses INT NOT NULL DEFAULT 0,
    failed_courses INT NOT NULL DEFAULT 0,
    withdrawn_courses INT NOT NULL DEFAULT 0,
    incomplete_courses INT NOT NULL DEFAULT 0,
    a_grades INT NOT NULL DEFAULT 0,
    b_grades INT NOT NULL DEFAULT 0,
    c_grades INT NOT NULL DEFAULT 0,
    d_grades INT NOT NULL DEFAULT 0,
    f_grades INT NOT NULL DEFAULT 0,
    academic_standing ENUM('excellent', 'good', 'satisfactory', 'probation', 'suspension', 'dismissal', 'warning') NULL,
    required_gpa DECIMAL(3,2) NULL,
    meets_gpa_requirement BOOLEAN NOT NULL DEFAULT TRUE,
    gpa_deficit DECIMAL(4,3) NULL,
    academic_year VARCHAR(10) NULL,
    year_level INT NULL,
    semester_type ENUM('fall', 'spring', 'summer', 'winter') NULL,
    class_rank INT NULL,
    class_size INT NULL,
    percentile DECIMAL(5,2) NULL,
    program_rank INT NULL,
    program_class_size INT NULL,
    credits_needed_to_graduate DECIMAL(6,2) NULL,
    completion_percentage DECIMAL(5,2) NULL,
    projected_graduation_date DATE NULL,
    on_track_to_graduate BOOLEAN NOT NULL DEFAULT TRUE,
    includes_transfer_credits BOOLEAN NOT NULL DEFAULT FALSE,
    includes_repeated_courses BOOLEAN NOT NULL DEFAULT FALSE,
    dean_list_eligible BOOLEAN NOT NULL DEFAULT FALSE,
    honors_eligible BOOLEAN NOT NULL DEFAULT FALSE,
    graduation_honors_eligible BOOLEAN NOT NULL DEFAULT FALSE,
    calculated_at TIMESTAMP NOT NULL,
    calculated_by_lecture_id BIGINT UNSIGNED NULL,
    calculation_parameters JSON NULL,
    calculation_notes TEXT NULL,
    is_verified BOOLEAN NOT NULL DEFAULT FALSE,
    verified_at TIMESTAMP NULL,
    verified_by_lecture_id BIGINT UNSIGNED NULL,
    is_current BOOLEAN NOT NULL DEFAULT TRUE,
    previous_gpa DECIMAL(4,3) NULL,
    gpa_change DECIMAL(4,3) NULL,
    gpa_trend ENUM('improving', 'declining', 'stable') NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    PRIMARY KEY (id),
    UNIQUE KEY idx_gpa_calculations_unique (student_id, semester_id, calculation_type),
    INDEX idx_gpa_calculations_student_type_current (student_id, calculation_type, is_current),
    INDEX idx_gpa_calculations_student_semester_type (student_id, semester_id, calculation_type),
    INDEX idx_gpa_calculations_student_year_type (student_id, academic_year, calculation_type),
    INDEX idx_gpa_calculations_program_semester_gpa (program_id, semester_id, gpa),
    INDEX idx_gpa_calculations_standing_gpa (academic_standing, gpa),
    INDEX idx_gpa_calculations_dean_list_gpa (dean_list_eligible, gpa),
    INDEX idx_gpa_calculations_class_ranking (class_rank, class_size),
    INDEX idx_gpa_calculations_program_ranking (program_rank, program_class_size),
    INDEX idx_gpa_calculations_calc_date_current (calculated_at, is_current),
    INDEX idx_gpa_calculations_verification (is_verified, verified_at),
    INDEX idx_gpa_calculations_completion_track (completion_percentage, on_track_to_graduate),
    INDEX idx_gpa_calculations_student_type_semester_current (student_id, calculation_type, semester_id, is_current),
    INDEX idx_gpa_calculations_student_progress (student_id, gpa, credit_hours_earned, is_current),
    CONSTRAINT fk_gpa_calculations_student_id FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    CONSTRAINT fk_gpa_calculations_semester_id FOREIGN KEY (semester_id) REFERENCES semesters(id) ON DELETE SET NULL,
    CONSTRAINT fk_gpa_calculations_program_id FOREIGN KEY (program_id) REFERENCES programs(id) ON DELETE SET NULL,
    CONSTRAINT fk_gpa_calculations_calculated_by FOREIGN KEY (calculated_by_lecture_id) REFERENCES lectures(id) ON DELETE SET NULL,
    CONSTRAINT fk_gpa_calculations_verified_by FOREIGN KEY (verified_by_lecture_id) REFERENCES lectures(id) ON DELETE SET NULL,
    CONSTRAINT check_gpa_valid CHECK (gpa IS NULL OR (gpa >= 0 AND gpa <= 4)),
    CONSTRAINT check_quality_points_positive CHECK (quality_points >= 0),
    CONSTRAINT check_credit_hours_positive CHECK (
        credit_hours_attempted >= 0 AND
        credit_hours_earned >= 0 AND
        credit_hours_gpa >= 0 AND
        credit_hours_earned <= credit_hours_attempted
    ),
    CONSTRAINT check_course_counts CHECK (
        total_courses >= 0 AND
        completed_courses >= 0 AND
        failed_courses >= 0 AND
        withdrawn_courses >= 0 AND
        incomplete_courses >= 0 AND
        (completed_courses + failed_courses + withdrawn_courses + incomplete_courses) <= total_courses
    ),
    CONSTRAINT check_grade_counts CHECK (
        a_grades >= 0 AND b_grades >= 0 AND c_grades >= 0 AND d_grades >= 0 AND f_grades >= 0
    ),
    CONSTRAINT check_percentile CHECK (percentile IS NULL OR (percentile >= 0 AND percentile <= 100)),
    CONSTRAINT check_completion_percentage CHECK (completion_percentage IS NULL OR (completion_percentage >= 0 AND completion_percentage <= 100))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
