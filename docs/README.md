# 📚 Swinburne Project Management System - Documentation

Welcome to the comprehensive documentation for the Swinburne Project Management System. This documentation is available in both English and Vietnamese.

## 🌐 Language Selection

### English Documentation
- **[📥 Installation Guide](installation-en.md)** - Complete setup and installation instructions
- **[🛠️ Development Guide](development-en.md)** - Development standards, workflows, and best practices
- **[🚀 Deployment Guide](deployment-en.md)** - Production deployment with Docker and CI/CD
- **[👥 User Guide](user-guide-en.md)** - System functionality and user instructions
- **[🔌 API Documentation](api-documentation-en.md)** - RESTful API endpoints and integration guide

### Vietnamese Documentation (Tài liệu tiếng Việt)
- **[📥 Hướng Dẫn Cài Đặt](installation-vi.md)** - Hướng dẫn thiết lập và cài đặt hoàn chỉnh
- **[🛠️ Hướng Dẫn Phát Triển](development-vi.md)** - Tiêu chuẩn phát triển, quy trình và best practices
- **[🚀 Hướng Dẫn Triển Khai](deployment-vi.md)** - Triển khai production với Docker và CI/CD
- **[👥 Hướng Dẫn Người Dùng](user-guide-vi.md)** - Chức năng hệ thống và hướng dẫn người dùng
- **[🔌 Tài Liệu API](api-documentation-vi.md)** - RESTful API endpoints và hướng dẫn tích hợp

## 🚀 Quick Start

### For Developers
1. **Setup**: Follow the [Installation Guide](installation-en.md) | [Hướng Dẫn Cài Đặt](installation-vi.md)
2. **Development**: Read the [Development Guide](development-en.md) | [Hướng Dẫn Phát Triển](development-vi.md)
3. **Deploy**: Use the [Deployment Guide](deployment-en.md) | [Hướng Dẫn Triển Khai](deployment-vi.md)

### For End Users
1. **Getting Started**: Check the [User Guide](user-guide-en.md) | [Hướng Dẫn Người Dùng](user-guide-vi.md)
2. **System Features**: Learn about user management, import/export, and role-based access
3. **Troubleshooting**: Find solutions to common issues

### For Integrators
1. **API Integration**: Review the [API Documentation](api-documentation-en.md) | [Tài Liệu API](api-documentation-vi.md)
2. **Authentication**: Understand token-based authentication
3. **Data Models**: Learn about user, campus, and role structures

## 🏗️ System Overview

### Technology Stack
- **Backend**: Laravel 12, PHP 8.4
- **Frontend**: Vue.js 3, TypeScript, TailwindCSS
- **Web Server**: FrankenPHP
- **Database**: MySQL 8.0
- **Cache**: Redis
- **Development**: Docker Compose, Vite HMR

### Key Features
- **Multi-campus Support**: Manage multiple university campuses
- **Role-based Access Control**: Granular permissions system
- **User Management**: Complete CRUD operations with import/export
- **RESTful API**: Full API for external integrations
- **Modern UI**: Responsive design with Vue.js 3 and TailwindCSS

## 📋 Documentation Structure

### Core Documentation
```
docs/
├── README.md                    # This file - documentation index
├── installation-en.md           # English installation guide
├── installation-vi.md           # Vietnamese installation guide
├── development-en.md            # English development guide
├── development-vi.md            # Vietnamese development guide
├── deployment-en.md             # English deployment guide
├── deployment-vi.md             # Vietnamese deployment guide
├── user-guide-en.md             # English user guide
├── user-guide-vi.md             # Vietnamese user guide
├── api-documentation-en.md      # English API documentation
├── api-documentation-vi.md      # Vietnamese API documentation
└── technical/                   # Technical implementation details
```

### Technical Documentation
The `technical/` directory contains detailed implementation guides and specifications:
- Database schema and architecture
- CRUD implementation patterns
- Permission system details
- Import/export functionality
- Project phase documentation

## 🎯 Getting Help

### Documentation Issues
If you find any issues with the documentation:
1. Check if the information exists in the alternative language version
2. Look for related information in the technical documentation
3. Contact the development team for clarification

### System Support
For system-related support:
- **Installation Issues**: Check the installation guide troubleshooting section
- **Development Questions**: Review the development guide and best practices
- **Deployment Problems**: Follow the deployment guide step-by-step procedures
- **User Support**: Refer users to the user guide for system functionality
- **API Integration**: Use the API documentation and examples

### Contact Information
- **Development Team**: Contact through your organization's channels
- **System Administrator**: For access and permission issues
- **Technical Support**: For bug reports and technical problems

## 🔄 Documentation Updates

This documentation is actively maintained and updated with each system release. 

### Version Information
- **Last Updated**: {{ date('Y-m-d') }}
- **System Version**: Laravel 12 + Vue.js 3
- **Documentation Version**: 2.0

### Contributing to Documentation
If you need to update or improve the documentation:
1. Follow the bilingual structure (English and Vietnamese)
2. Maintain consistency between language versions
3. Update both technical and user-facing documentation
4. Test all code examples and procedures

## 📖 Additional Resources

### External Documentation
- **Laravel Framework**: https://laravel.com/docs
- **Vue.js Framework**: https://vuejs.org/guide/
- **Inertia.js**: https://inertiajs.com/
- **TailwindCSS**: https://tailwindcss.com/docs
- **Docker**: https://docs.docker.com/

### Development Tools
- **Laravel Herd**: https://herd.laravel.com/
- **VS Code Extensions**: PHP, Vue.js, Laravel, Docker
- **Postman**: For API testing and development

---

## 🌟 Welcome to Swinburne Project Management System

This system represents a modern approach to educational management, combining powerful backend capabilities with an intuitive user interface. Whether you're a developer, administrator, or end user, this documentation will guide you through every aspect of the system.

Choose your preferred language and documentation section to get started!

---

*For the most up-to-date information, always refer to the latest version of this documentation.*
