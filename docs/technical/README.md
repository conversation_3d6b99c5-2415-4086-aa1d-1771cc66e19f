# 🔧 Technical Documentation

This directory contains detailed technical documentation, implementation guides, and legacy documentation files.

## 📋 Current Technical Documentation

### Implementation Guides
- **[CRUD_IMPLEMENTATION_GUIDE.md](CRUD_IMPLEMENTATION_GUIDE.md)** - Standardized CRUD implementation patterns
- **[DEVELOPMENT_CHECKLIST_AND_GUIDELINES.md](DEVELOPMENT_CHECKLIST_AND_GUIDELINES.md)** - Comprehensive development checklist
- **[PERMISSION_SYSTEM.md](PERMISSION_SYSTEM.md)** - Role-based permission system details

### Database and Architecture
- **[DATABASE_ARCHITECTURE_UPDATED.md](DATABASE_ARCHITECTURE_UPDATED.md)** - Database schema and relationships
- **[CURRICULUM_SCHEMA_IMPLEMENTATION.md](CURRICULUM_SCHEMA_IMPLEMENTATION.md)** - Curriculum management schema
- **[CURRICULUM_CRUD_IMPLEMENTATION.md](CURRICULUM_CRUD_IMPLEMENTATION.md)** - Curriculum CRUD operations

### Project Phases
- **[01_SEMESTER_AND_CURRICULUM_MANAGEMENT.md](01_SEMESTER_AND_CURRICULUM_MANAGEMENT.md)** - Phase 1: Semester and curriculum management
- **[02_CAMPUS_AND_CLASSROOM_MANAGEMENT.md](02_CAMPUS_AND_CLASSROOM_MANAGEMENT.md)** - Phase 2: Campus and classroom management
- **[03_USER_MANAGEMENT_SYSTEM.md](03_USER_MANAGEMENT_SYSTEM.md)** - Phase 3: User management system
- **[04_TEACHING_MANAGEMENT_SYSTEM.md](04_TEACHING_MANAGEMENT_SYSTEM.md)** - Phase 4: Teaching management
- **[05_STUDENT_ENROLLMENT_SYSTEM.md](05_STUDENT_ENROLLMENT_SYSTEM.md)** - Phase 5: Student enrollment

### Specialized Features
- **[ELECTIVE_MANAGEMENT.md](ELECTIVE_MANAGEMENT.md)** - Elective course management
- **[PREREQUISITES_IMPORT_GUIDE.md](PREREQUISITES_IMPORT_GUIDE.md)** - Prerequisites import functionality
- **[COMPLEX_PREREQUISITE_EXAMPLES.md](COMPLEX_PREREQUISITE_EXAMPLES.md)** - Complex prerequisite examples
- **[STEP_BY_STEP_TEMPLATE_GUIDE.md](STEP_BY_STEP_TEMPLATE_GUIDE.md)** - Template generation guide

### Implementation Summaries
- **[IMPLEMENTATION_SUMMARY.md](IMPLEMENTATION_SUMMARY.md)** - Overall implementation summary
- **[SOLUTION_SUMMARY.md](SOLUTION_SUMMARY.md)** - Solution summary and fixes
- **[curriculum-implementation-summary.md](curriculum-implementation-summary.md)** - Curriculum implementation details
- **[curriculum-specialization-implementation-summary.md](curriculum-specialization-implementation-summary.md)** - Specialization implementation
- **[semester-system-implementation.md](semester-system-implementation.md)** - Semester system details

## 🐳 Docker and Deployment

### Docker Documentation
- **[DOCKER_GUIDE.md](DOCKER_GUIDE.md)** - Comprehensive Docker environment guide
- **[FRANKENPHP_MIGRATION_GUIDE.md](FRANKENPHP_MIGRATION_GUIDE.md)** - FrankenPHP web server migration
- **[DOCKER_CLEANUP_SUMMARY.md](DOCKER_CLEANUP_SUMMARY.md)** - Docker cleanup and optimization

### Deployment Documentation
- **[original-DEPLOYMENT.md](original-DEPLOYMENT.md)** - Original detailed deployment guide
- **[STEP_BY_STEP_DEPLOY.md](STEP_BY_STEP_DEPLOY.md)** - Step-by-step deployment instructions

### Legacy Documentation
- **[original-README.md](original-README.md)** - Original project README
- **[README_EMPLOYEE_GUIDE.md](README_EMPLOYEE_GUIDE.md)** - Employee guide for user management

## 🔧 Specialized Guides

### User Interface
- **[SIDEBAR_README.md](SIDEBAR_README.md)** - Sidebar navigation implementation

### System Features
- **[semester-activation-logic.md](semester-activation-logic.md)** - Semester activation logic
- **[curriculum-specialization-examples.md](curriculum-specialization-examples.md)** - Curriculum specialization examples

### Bug Fixes and Solutions
- **[GOOGLE_OAUTH_FIX.md](GOOGLE_OAUTH_FIX.md)** - Google OAuth integration fixes

## 📖 How to Use This Documentation

### For Developers
1. **Start with**: [DEVELOPMENT_CHECKLIST_AND_GUIDELINES.md](DEVELOPMENT_CHECKLIST_AND_GUIDELINES.md)
2. **Implementation**: Use [CRUD_IMPLEMENTATION_GUIDE.md](CRUD_IMPLEMENTATION_GUIDE.md) for new features
3. **Database**: Reference [DATABASE_ARCHITECTURE_UPDATED.md](DATABASE_ARCHITECTURE_UPDATED.md)
4. **Permissions**: Understand [PERMISSION_SYSTEM.md](PERMISSION_SYSTEM.md)

### For System Architects
1. **Project Phases**: Review phase documentation (01-05)
2. **Architecture**: Study database and system architecture docs
3. **Implementation**: Check implementation summaries

### For DevOps Engineers
1. **Docker**: Use [DOCKER_GUIDE.md](DOCKER_GUIDE.md) and [FRANKENPHP_MIGRATION_GUIDE.md](FRANKENPHP_MIGRATION_GUIDE.md)
2. **Deployment**: Follow [original-DEPLOYMENT.md](original-DEPLOYMENT.md) and [STEP_BY_STEP_DEPLOY.md](STEP_BY_STEP_DEPLOY.md)
3. **Maintenance**: Reference cleanup and optimization guides

## 🔗 Related Documentation

### Main Documentation
- **[Installation Guide](../installation-en.md)** - Setup and installation
- **[Development Guide](../development-en.md)** - Development standards
- **[Deployment Guide](../deployment-en.md)** - Production deployment
- **[User Guide](../user-guide-en.md)** - System usage
- **[API Documentation](../api-documentation-en.md)** - API integration

### Vietnamese Documentation
- **[Hướng Dẫn Cài Đặt](../installation-vi.md)**
- **[Hướng Dẫn Phát Triển](../development-vi.md)**
- **[Hướng Dẫn Triển Khai](../deployment-vi.md)**
- **[Hướng Dẫn Người Dùng](../user-guide-vi.md)**
- **[Tài Liệu API](../api-documentation-vi.md)**

## 📝 Documentation Maintenance

### Updating Technical Documentation
1. Keep implementation guides current with code changes
2. Update database documentation when schema changes
3. Maintain phase documentation as features are completed
4. Archive outdated documentation appropriately

### Legacy Documentation
Some files in this directory represent legacy documentation that has been superseded by the main documentation but is kept for reference:
- `original-README.md` - Original project README
- `original-DEPLOYMENT.md` - Original deployment guide
- `README_EMPLOYEE_GUIDE.md` - Legacy employee guide

---

*This technical documentation supports the main project documentation and provides detailed implementation guidance for developers and system architects.*
