{"level":"error","ts":1749501252.9320397,"logger":"http.log.access.log0","msg":"handled request","request":{"remote_ip":"************","remote_port":"37171","client_ip":"************","proto":"HTTP/2.0","method":"HEAD","host":"swinx.test","uri":"/","headers":{"User-Agent":["curl/8.7.1"],"Accept":["*/*"]},"tls":{"resumed":false,"version":772,"cipher_suite":4867,"proto":"h2","server_name":"swinx.test"}},"bytes_read":0,"user_id":"","duration":0.197286375,"size":0,"status":500,"resp_headers":{"X-Environment":["local-production"],"X-Frame-Options":["SAMEORIGIN"],"Cross-Origin-Resource-Policy":["same-origin"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Date":["Mon, 09 Jun 2025 20:34:12 GMT"],"Content-Type":["text/html; charset=UTF-8"],"X-Content-Type-Options":["nosniff"],"Cross-Origin-Opener-Policy":["same-origin"],"Cross-Origin-Embedder-Policy":["require-corp"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Cache-Control":["no-cache, private"],"X-Xss-Protection":["1; mode=block"],"X-Permitted-Cross-Domain-Policies":["none"],"Content-Security-Policy":["default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:;"],"Strict-Transport-Security":["max-age=3600; includeSubDomains"]}}
{"level":"info","ts":**********.8023186,"logger":"http.log.access.log0","msg":"handled request","request":{"remote_ip":"************","remote_port":"19772","client_ip":"************","proto":"HTTP/2.0","method":"GET","host":"swinx.test","uri":"/health","headers":{"User-Agent":["curl/8.7.1"],"Accept":["*/*"]},"tls":{"resumed":false,"version":772,"cipher_suite":4867,"proto":"h2","server_name":"swinx.test"}},"bytes_read":0,"user_id":"","duration":0.001200958,"size":7,"status":200,"resp_headers":{"Cross-Origin-Resource-Policy":["same-origin"],"X-Xss-Protection":["1; mode=block"],"X-Frame-Options":["SAMEORIGIN"],"X-Permitted-Cross-Domain-Policies":["none"],"Cross-Origin-Embedder-Policy":["require-corp"],"Cross-Origin-Opener-Policy":["same-origin"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"],"X-Environment":["local-production"],"Strict-Transport-Security":["max-age=3600; includeSubDomains"],"X-Content-Type-Options":["nosniff"],"Content-Security-Policy":["default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:;"]}}
{"level":"error","ts":1749501303.6064186,"logger":"http.log.access.log0","msg":"handled request","request":{"remote_ip":"************","remote_port":"29603","client_ip":"************","proto":"HTTP/2.0","method":"GET","host":"swinx.test","uri":"/","headers":{"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Ch-Ua":["\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\""],"User-Agent":["Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Sec-Fetch-Dest":["document"],"Accept-Language":["en,en-US;q=0.9,vi;q=0.8,ko;q=0.7,af;q=0.6"],"Sec-Ch-Ua-Platform":["\"macOS\""],"Sec-Fetch-Mode":["navigate"],"Priority":["u=0, i"],"Upgrade-Insecure-Requests":["1"],"Sec-Fetch-Site":["none"],"Cache-Control":["max-age=0"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-User":["?1"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"swinx.test"}},"bytes_read":0,"user_id":"","duration":0.068727333,"size":2227,"status":500,"resp_headers":{"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Encoding":["zstd"],"Content-Security-Policy":["default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:;"],"X-Environment":["local-production"],"X-Frame-Options":["SAMEORIGIN"],"X-Permitted-Cross-Domain-Policies":["none"],"Date":["Mon, 09 Jun 2025 20:35:03 GMT"],"Vary":["Accept-Encoding"],"Cross-Origin-Opener-Policy":["same-origin"],"Cache-Control":["no-cache, private"],"Strict-Transport-Security":["max-age=3600; includeSubDomains"],"X-Content-Type-Options":["nosniff"],"Content-Type":["text/html; charset=UTF-8"],"Cross-Origin-Embedder-Policy":["require-corp"],"Cross-Origin-Resource-Policy":["same-origin"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Xss-Protection":["1; mode=block"]}}
{"level":"info","ts":1749501304.0244617,"logger":"http.log.access.log0","msg":"handled request","request":{"remote_ip":"************","remote_port":"29603","client_ip":"************","proto":"HTTP/2.0","method":"GET","host":"swinx.test","uri":"/favicon.ico","headers":{"Sec-Ch-Ua-Platform":["\"macOS\""],"Accept":["image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8"],"Sec-Fetch-Site":["same-origin"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Ch-Ua":["\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\""],"Referer":["https://swinx.test/"],"Accept-Language":["en,en-US;q=0.9,vi;q=0.8,ko;q=0.7,af;q=0.6"],"Sec-Fetch-Mode":["no-cors"],"Priority":["u=1, i"],"User-Agent":["Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Dest":["image"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"swinx.test"}},"bytes_read":0,"user_id":"","duration":0.01600625,"size":1317,"status":200,"resp_headers":{"Cross-Origin-Resource-Policy":["same-origin"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["image/vnd.microsoft.icon"],"Last-Modified":["Fri, 02 May 2025 14:38:30 GMT"],"Cross-Origin-Embedder-Policy":["require-corp"],"X-Frame-Options":["SAMEORIGIN"],"Content-Encoding":["zstd"],"X-Content-Type-Options":["nosniff"],"Content-Security-Policy":["default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:;"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Environment":["local-production"],"Expires":["1 hour"],"Cache-Control":["public, max-age=3600"],"Etag":["\"d9lr2m6v0dmo3b2-zstd\""],"Vary":["Accept-Encoding"],"X-Xss-Protection":["1; mode=block"],"Strict-Transport-Security":["max-age=3600; includeSubDomains"],"X-Permitted-Cross-Domain-Policies":["none"],"Cross-Origin-Opener-Policy":["same-origin"]}}
{"level":"error","ts":1749501306.616165,"logger":"http.log.access.log0","msg":"handled request","request":{"remote_ip":"************","remote_port":"29603","client_ip":"************","proto":"HTTP/2.0","method":"GET","host":"swinx.test","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Sec-Fetch-Site":["none"],"Sec-Fetch-User":["?1"],"Cache-Control":["max-age=0"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Mode":["navigate"],"Priority":["u=0, i"],"Sec-Ch-Ua":["\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\""],"Upgrade-Insecure-Requests":["1"],"Sec-Fetch-Dest":["document"],"Accept-Language":["en,en-US;q=0.9,vi;q=0.8,ko;q=0.7,af;q=0.6"],"Sec-Ch-Ua-Platform":["\"macOS\""],"Accept-Encoding":["gzip, deflate, br, zstd"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"swinx.test"}},"bytes_read":0,"user_id":"","duration":0.04830225,"size":2227,"status":500,"resp_headers":{"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Environment":["local-production"],"X-Xss-Protection":["1; mode=block"],"Content-Encoding":["zstd"],"Content-Security-Policy":["default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:;"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Cache-Control":["no-cache, private"],"Content-Type":["text/html; charset=UTF-8"],"Cross-Origin-Embedder-Policy":["require-corp"],"Cross-Origin-Opener-Policy":["same-origin"],"Strict-Transport-Security":["max-age=3600; includeSubDomains"],"X-Permitted-Cross-Domain-Policies":["none"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Date":["Mon, 09 Jun 2025 20:35:06 GMT"],"Vary":["Accept-Encoding"],"Cross-Origin-Resource-Policy":["same-origin"]}}
{"level":"info","ts":1749501306.7271485,"logger":"http.log.access.log0","msg":"handled request","request":{"remote_ip":"************","remote_port":"29603","client_ip":"************","proto":"HTTP/2.0","method":"GET","host":"swinx.test","uri":"/favicon.ico","headers":{"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Fetch-Mode":["no-cors"],"Referer":["https://swinx.test/"],"Accept-Language":["en,en-US;q=0.9,vi;q=0.8,ko;q=0.7,af;q=0.6"],"Priority":["u=1, i"],"User-Agent":["Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua-Mobile":["?0"],"Accept":["image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Dest":["image"],"Sec-Ch-Ua-Platform":["\"macOS\""],"Sec-Ch-Ua":["\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\""]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"swinx.test"}},"bytes_read":0,"user_id":"","duration":0.023839875,"size":1317,"status":200,"resp_headers":{"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Security-Policy":["default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:;"],"X-Environment":["local-production"],"X-Content-Type-Options":["nosniff"],"Expires":["1 hour"],"Vary":["Accept-Encoding"],"Content-Type":["image/vnd.microsoft.icon"],"Cross-Origin-Embedder-Policy":["require-corp"],"Cross-Origin-Opener-Policy":["same-origin"],"Strict-Transport-Security":["max-age=3600; includeSubDomains"],"X-Frame-Options":["SAMEORIGIN"],"Cache-Control":["public, max-age=3600"],"Etag":["\"d9lr2m6v0dmo3b2-zstd\""],"Cross-Origin-Resource-Policy":["same-origin"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Xss-Protection":["1; mode=block"],"Last-Modified":["Fri, 02 May 2025 14:38:30 GMT"],"Content-Encoding":["zstd"],"X-Permitted-Cross-Domain-Policies":["none"]}}
{"level":"info","ts":**********.9208922,"logger":"http.log.access.log0","msg":"handled request","request":{"remote_ip":"************","remote_port":"63376","client_ip":"************","proto":"HTTP/2.0","method":"GET","host":"swinx.test","uri":"/health","headers":{"Sec-Ch-Ua":["\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\""],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Sec-Fetch-Site":["none"],"Sec-Fetch-User":["?1"],"Sec-Fetch-Dest":["document"],"Upgrade-Insecure-Requests":["1"],"Accept-Language":["en,en-US;q=0.9,vi;q=0.8,ko;q=0.7,af;q=0.6"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"macOS\""],"Accept-Encoding":["gzip, deflate, br, zstd"],"User-Agent":["Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Mode":["navigate"],"Priority":["u=0, i"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"swinx.test"}},"bytes_read":0,"user_id":"","duration":0.001169167,"size":7,"status":200,"resp_headers":{"X-Permitted-Cross-Domain-Policies":["none"],"Content-Security-Policy":["default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:;"],"Cross-Origin-Embedder-Policy":["require-corp"],"Cross-Origin-Opener-Policy":["same-origin"],"Cross-Origin-Resource-Policy":["same-origin"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Xss-Protection":["1; mode=block"],"Strict-Transport-Security":["max-age=3600; includeSubDomains"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"X-Environment":["local-production"]}}
{"level":"error","ts":1749501334.7012458,"logger":"http.log.access.log0","msg":"handled request","request":{"remote_ip":"************","remote_port":"63376","client_ip":"************","proto":"HTTP/2.0","method":"GET","host":"swinx.test","uri":"/","headers":{"Sec-Ch-Ua-Mobile":["?0"],"User-Agent":["Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Accept-Language":["en,en-US;q=0.9,vi;q=0.8,ko;q=0.7,af;q=0.6"],"Priority":["u=0, i"],"Sec-Ch-Ua-Platform":["\"macOS\""],"Sec-Fetch-User":["?1"],"Sec-Fetch-Mode":["navigate"],"Sec-Fetch-Dest":["document"],"Upgrade-Insecure-Requests":["1"],"Sec-Fetch-Site":["none"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Ch-Ua":["\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\""]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"swinx.test"}},"bytes_read":0,"user_id":"","duration":0.053306917,"size":2227,"status":500,"resp_headers":{"Content-Encoding":["zstd"],"Vary":["Accept-Encoding"],"Cross-Origin-Opener-Policy":["same-origin"],"Cache-Control":["no-cache, private"],"Date":["Mon, 09 Jun 2025 20:35:34 GMT"],"Cross-Origin-Resource-Policy":["same-origin"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Content-Security-Policy":["default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:;"],"Cross-Origin-Embedder-Policy":["require-corp"],"X-Content-Type-Options":["nosniff"],"X-Permitted-Cross-Domain-Policies":["none"],"X-Environment":["local-production"],"X-Xss-Protection":["1; mode=block"],"Strict-Transport-Security":["max-age=3600; includeSubDomains"],"X-Frame-Options":["SAMEORIGIN"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/html; charset=UTF-8"]}}
