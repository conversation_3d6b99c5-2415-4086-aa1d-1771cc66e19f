# 🧩 Prompt Template — Phase 1: Student Creation and Auto-Admission System

## 🎯 Objective:
- Create a unified student creation and admission workflow (students are admitted upon creation)
- Implement automatic role assignment for new students
- Follow the existing Service-Request-Resource Pattern used in the Swinburne system
- Create comprehensive frontend implementation using Vue 3 + Inertia.js
- Filter students by current campus session

## ✅ Required Structure (Based on Existing Project Pattern)

### Backend Structure
```
app/
├── Services/
│   └── StudentService.php (extend existing)
├── Http/
│   ├── Controllers/
│   │   └── Api/
│   │       └── StudentController.php (extend existing)
│   ├── Requests/
│   │   └── Student/
│   │       └── StoreStudentRequest.php (update existing)
│   └── Resources/
│       └── Student/
│           └── StudentResource.php (update existing)
├── Models/
│   └── Student.php (already exists - status field available)
```

### Frontend Structure
```
resources/js/
├── pages/
│   └── students/
│       ├── Index.vue (existing - update with campus filtering)
│       └── Create.vue (update for unified creation)
├── types/
│   └── models.ts (add student creation types)
└── utils/
    └── routes.ts (add student creation routes)
```

---

# 🖥️ BACKEND IMPLEMENTATION

## 🔧 Prompt 1: Extend StudentService with Unified Creation Logic

Extend the existing `StudentService` class in `app/Services/StudentService.php`.

Add a new method for unified student creation and admission:

```php
/**
 * Create and admit a student in one step
 */
public function createAdmittedStudent(array $data): Student
{
    return DB::transaction(function () use ($data) {
        // Ensure campus_id from session if not provided
        $campusId = $data['campus_id'] ?? session()->get('campus_id');
        
        if (!$campusId) {
            throw new \InvalidArgumentException('Campus ID is required either in data or session');
        }

        // Generate unique student ID
        $studentId = $this->generateStudentId($campusId);

        // Create student with admitted status
        $student = Student::create([
            'student_id' => $studentId,
            'full_name' => $data['full_name'],
            'email' => $data['email'],
            'phone' => $data['phone'] ?? null,
            'campus_id' => $campusId,
            'program_id' => $data['program_id'],
            'specialization_id' => $data['specialization_id'] ?? null,
            'curriculum_version_id' => $data['curriculum_version_id'],
            'status' => 'admitted', // Always admitted upon creation
            'admission_date' => $data['admission_date'],
            'admission_notes' => $data['notes'] ?? null,
            'expected_graduation_date' => $data['expected_graduation_date'] ?? null,
        ]);

        // Assign student role to the campus
        $this->assignStudentRole($student);

        // Log the creation and admission
        Log::info('Student created and admitted', [
            'student_id' => $student->id,
            'student_code' => $student->student_id,
            'campus_id' => $campusId,
            'admission_date' => $data['admission_date'],
        ]);

        return $student->fresh(['campus', 'program', 'specialization', 'curriculumVersion']);
    });
}

/**
 * Assign student role in campus
 */
private function assignStudentRole(Student $student): void
{
    $studentRole = \App\Models\Role::where('code', 'sinh_vien')->first();
    
    if ($studentRole) {
        \App\Models\CampusUserRole::create([
            'user_id' => $student->id,
            'role_id' => $studentRole->id,
            'campus_id' => $student->campus_id,
            'assigned_at' => now(),
        ]);
    }
}

/**
 * Generate unique student ID for campus
 */
private function generateStudentId(int $campusId): string
{
    $campus = \App\Models\Campus::find($campusId);
    $campusCode = $campus ? $campus->code : 'STU';
    $year = date('Y');
    
    // Find the last student ID for this campus and year
    $lastStudent = Student::where('campus_id', $campusId)
        ->where('student_id', 'like', "{$campusCode}{$year}%")
        ->orderBy('student_id', 'desc')
        ->first();
    
    if ($lastStudent) {
        $lastNumber = (int) substr($lastStudent->student_id, -4);
        $newNumber = $lastNumber + 1;
    } else {
        $newNumber = 1;
    }
    
    return $campusCode . $year . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
}

/**
 * Get students filtered by campus
 */
public function getStudentsByCampus(int $campusId, array $filters = []): \Illuminate\Pagination\LengthAwarePaginator
{
    $query = Student::with(['campus', 'program', 'specialization'])
        ->where('campus_id', $campusId);

    // Apply additional filters
    if (!empty($filters['search'])) {
        $search = $filters['search'];
        $query->where(function ($q) use ($search) {
            $q->where('full_name', 'like', "%{$search}%")
              ->orWhere('student_id', 'like', "%{$search}%")
              ->orWhere('email', 'like', "%{$search}%");
        });
    }

    if (!empty($filters['status'])) {
        $query->where('status', $filters['status']);
    }

    if (!empty($filters['program_id'])) {
        $query->where('program_id', $filters['program_id']);
    }

    return $query->orderBy('created_at', 'desc')->paginate(15);
}
```

Import these additional classes at the top:
```php
use App\Models\Role;
use App\Models\CampusUserRole;
use App\Models\Campus;
```

---

## 📥 Prompt 2: Update StoreStudentRequest

Update the existing `StoreStudentRequest` in `app/Http/Requests/Student/StoreStudentRequest.php`:

```php
<?php

declare(strict_types=1);

namespace App\Http\Requests\Student;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreStudentRequest extends FormRequest
{
    public function authorize(): bool
    {
        // TODO: Add proper permission check for student creation
        return true; // For now, allow all authenticated users
    }

    public function rules(): array
    {
        return [
            'full_name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'unique:students,email'],
            'phone' => ['nullable', 'string', 'max:20'],
            'campus_id' => ['nullable', 'exists:campuses,id'], // Optional if taken from session
            'program_id' => ['required', 'exists:programs,id'],
            'specialization_id' => ['nullable', 'exists:specializations,id'],
            'curriculum_version_id' => ['required', 'exists:curriculum_versions,id'],
            'admission_date' => ['required', 'date', 'before_or_equal:today'],
            'notes' => ['nullable', 'string', 'max:1000'],
            'expected_graduation_date' => ['nullable', 'date', 'after:admission_date'],
        ];
    }

    public function messages(): array
    {
        return [
            'full_name.required' => 'Full name is required',
            'full_name.max' => 'Full name cannot exceed 255 characters',
            'email.required' => 'Email is required',
            'email.email' => 'Please provide a valid email address',
            'email.unique' => 'This email is already registered',
            'program_id.required' => 'Program selection is required',
            'program_id.exists' => 'Selected program is invalid',
            'curriculum_version_id.required' => 'Curriculum version is required',
            'curriculum_version_id.exists' => 'Selected curriculum version is invalid',
            'admission_date.required' => 'Admission date is required',
            'admission_date.date' => 'Please provide a valid admission date',
            'admission_date.before_or_equal' => 'Admission date cannot be in the future',
            'expected_graduation_date.after' => 'Expected graduation date must be after admission date',
            'notes.max' => 'Notes cannot exceed 1000 characters',
        ];
    }

    protected function prepareForValidation(): void
    {
        // Format dates
        if ($this->has('admission_date') && $this->admission_date) {
            $this->merge([
                'admission_date' => date('Y-m-d', strtotime($this->admission_date))
            ]);
        }

        if ($this->has('expected_graduation_date') && $this->expected_graduation_date) {
            $this->merge([
                'expected_graduation_date' => date('Y-m-d', strtotime($this->expected_graduation_date))
            ]);
        }

        // Use session campus_id if not provided
        if (!$this->has('campus_id') || !$this->campus_id) {
            $this->merge([
                'campus_id' => session()->get('campus_id')
            ]);
        }
    }
}
```

---

## 🧩 Prompt 3: Update API StudentController

Update the existing `StudentController` in `app/Http/Controllers/Api/StudentController.php`:

```php
/**
 * Display a listing of students for current campus
 */
public function index(Request $request): JsonResponse
{
    $campusId = session()->get('current_campus_id');
    
    if (!$campusId) {
        return response()->json([
            'success' => false,
            'message' => 'No campus selected',
        ], 400);
    }

    $filters = $request->only(['search', 'status', 'program_id']);
    $students = $this->studentService->getStudentsByCampus($campusId, $filters);

    return response()->json([
        'success' => true,
        'data' => [
            'students' => StudentResource::collection($students->items()),
            'pagination' => [
                'current_page' => $students->currentPage(),
                'last_page' => $students->lastPage(),
                'per_page' => $students->perPage(),
                'total' => $students->total(),
            ],
        ],
    ]);
}

/**
 * Store a newly created and admitted student
 */
public function store(StoreStudentRequest $request): JsonResponse
{
    try {
        $student = $this->studentService->createAdmittedStudent($request->validated());

        return response()->json([
            'success' => true,
            'message' => 'Student created and admitted successfully',
            'data' => [
                'student' => new StudentResource($student),
            ]
        ], 201);

    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Failed to create student: ' . $e->getMessage(),
        ], 500);
    }
}

/**
 * Get students count by status for current campus
 */
public function stats(): JsonResponse
{
    $campusId = session()->get('current_campus_id');
    
    if (!$campusId) {
        return response()->json([
            'success' => false,
            'message' => 'No campus selected',
        ], 400);
    }

    $stats = Student::where('campus_id', $campusId)
        ->selectRaw('status, COUNT(*) as count')
        ->groupBy('status')
        ->pluck('count', 'status')
        ->toArray();

    return response()->json([
        'success' => true,
        'data' => [
            'total' => array_sum($stats),
            'by_status' => $stats,
        ],
    ]);
}
```

Add this import at the top of the file:
```php
use App\Http\Requests\Student\StoreStudentRequest;
```

---

## 📤 Prompt 4: Update StudentResource

Update the existing `StudentResource` in `app/Http/Resources/Student/StudentResource.php`:

```php
<?php

namespace App\Http\Resources\Student;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class StudentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'student_id' => $this->student_id,
            'full_name' => $this->full_name,
            'email' => $this->email,
            'phone' => $this->phone,
            'status' => $this->status,
            'admission_date' => $this->admission_date?->format('Y-m-d'),
            'admission_notes' => $this->admission_notes,
            'expected_graduation_date' => $this->expected_graduation_date?->format('Y-m-d'),
            
            // Related data
            'campus_id' => $this->campus_id,
            'program_id' => $this->program_id,
            'specialization_id' => $this->specialization_id,
            'curriculum_version_id' => $this->curriculum_version_id,
            
            // Relationships (when loaded)
            'campus' => $this->whenLoaded('campus', function () {
                return [
                    'id' => $this->campus->id,
                    'name' => $this->campus->name,
                    'code' => $this->campus->code,
                ];
            }),
            
            'program' => $this->whenLoaded('program', function () {
                return [
                    'id' => $this->program->id,
                    'name' => $this->program->name,
                    'code' => $this->program->code,
                ];
            }),
            
            'specialization' => $this->whenLoaded('specialization', function () {
                return [
                    'id' => $this->specialization->id,
                    'name' => $this->specialization->name,
                ];
            }),

            'curriculum_version' => $this->whenLoaded('curriculumVersion', function () {
                return [
                    'id' => $this->curriculumVersion->id,
                    'name' => $this->curriculumVersion->name,
                    'version' => $this->curriculumVersion->version,
                ];
            }),
            
            // Computed fields
            'display_name' => $this->full_name . ' (' . $this->student_id . ')',
            'status_label' => ucfirst(str_replace('_', ' ', $this->status)),
            
            // Timestamps
            'created_at' => $this->created_at->toIso8601String(),
            'updated_at' => $this->updated_at->toIso8601String(),
        ];
    }
}
```

---

## 🚦 Prompt 5: Update API Routes

Update `routes/api.php` to include the new student endpoints:

```php
// Student management routes
Route::group(['prefix' => 'students', 'middleware' => ['auth:sanctum']], function () {
    Route::get('/', [App\Http\Controllers\Api\StudentController::class, 'index'])
        ->name('api.students.index');
    Route::post('/', [App\Http\Controllers\Api\StudentController::class, 'store'])
        ->name('api.students.store');
    Route::get('/stats', [App\Http\Controllers\Api\StudentController::class, 'stats'])
        ->name('api.students.stats');
    Route::get('/{student}', [App\Http\Controllers\Api\StudentController::class, 'show'])
        ->name('api.students.show');
    Route::put('/{student}', [App\Http\Controllers\Api\StudentController::class, 'update'])
        ->name('api.students.update');
    Route::delete('/{student}', [App\Http\Controllers\Api\StudentController::class, 'destroy'])
        ->name('api.students.destroy');
});
```

This creates the endpoints:
- `GET /api/students` - List students for current campus
- `POST /api/students` - Create and admit new student
- `GET /api/students/stats` - Get student statistics for current campus
- `GET /api/students/{id}` - Show specific student
- `PUT /api/students/{id}` - Update student
- `DELETE /api/students/{id}` - Delete student

---

# 🎨 FRONTEND IMPLEMENTATION

## 🏗️ Prompt 6: Add TypeScript Types for Student Creation

Add the following types to `resources/js/types/models.ts`:

```typescript
// Update existing Student interface
export interface Student {
  id: number;
  student_id: string;
  full_name: string;
  email: string;
  phone?: string;
  status: 'admitted' | 'active' | 'inactive' | 'graduated' | 'dropped_out';
  admission_date: string; // ISO date string
  admission_notes?: string;
  expected_graduation_date?: string;
  
  // Relationships
  campus_id: number;
  program_id: number;
  specialization_id?: number;
  curriculum_version_id: number;
  
  campus?: Campus;
  program?: Program;
  specialization?: Specialization;
  curriculum_version?: CurriculumVersion;
  
  // Computed fields
  display_name: string;
  status_label: string;
  
  // Timestamps
  created_at: string;
  updated_at: string;
}

// Interface for student creation form
export interface StudentCreateForm {
  full_name: string;
  email: string;
  phone?: string;
  program_id: number;
  specialization_id?: number;
  curriculum_version_id: number;
  admission_date: string;
  notes?: string;
  expected_graduation_date?: string;
}

// For API responses
export interface StudentResponse {
  success: boolean;
  message: string;
  data?: {
    student: Student;
  };
}

export interface StudentsListResponse {
  success: boolean;
  data: {
    students: Student[];
    pagination: {
      current_page: number;
      last_page: number;
      per_page: number;
      total: number;
    };
  };
}

// Student statistics interface
export interface StudentStats {
  total: number;
  by_status: Record<string, number>;
}
```

---

## 🛠️ Prompt 7: Add Routes to Route Utility

Update `resources/js/utils/routes.ts` to include student routes:

```typescript
// Add to existing route functions
export const studentRoutes = {
  index: () => route('api.students.index'),
  store: () => route('api.students.store'),
  show: (studentId: number) => route('api.students.show', studentId),
  update: (studentId: number) => route('api.students.update', studentId),
  destroy: (studentId: number) => route('api.students.destroy', studentId),
  stats: () => route('api.students.stats'),
};

// Helper functions for student operations
export const createStudent = () => route('api.students.store');
export const getStudents = () => route('api.students.index');
export const getStudentStats = () => route('api.students.stats');
```

---

## 🧩 Prompt 8: Create Student Creation Form Component

Create `resources/js/pages/students/Create.vue`:

```vue
<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { Head, useForm as useInertiaForm } from '@inertiajs/vue3';
import { Form } from 'vee-validate';
import { toTypedSchema } from '@vee-validate/zod';
import * as z from 'zod';
import { format } from 'date-fns';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import type { Program, Specialization, CurriculumVersion } from '@/types/models';
import { studentRoutes } from '@/utils/routes';
import { useApi } from '@/composables/useApiRequest';

interface Props {
  programs: Program[];
  specializations: Specialization[];
  curriculumVersions: CurriculumVersion[];
}

const props = defineProps<Props>();
const api = useApi();

// Validation schema using Zod
const createStudentSchema = toTypedSchema(
  z.object({
    full_name: z.string().min(1, 'Full name is required').max(255),
    email: z.string().email('Invalid email address'),
    phone: z.string().optional(),
    program_id: z.string().min(1, 'Program is required'),
    specialization_id: z.string().optional(),
    curriculum_version_id: z.string().min(1, 'Curriculum version is required'),
    admission_date: z.string().min(1, 'Admission date is required'),
    notes: z.string().optional(),
    expected_graduation_date: z.string().optional(),
  })
);

// Inertia form for submission
const inertiaForm = useInertiaForm({
  full_name: '',
  email: '',
  phone: '',
  program_id: '',
  specialization_id: '',
  curriculum_version_id: '',
  admission_date: format(new Date(), 'yyyy-MM-dd'),
  notes: '',
  expected_graduation_date: '',
});

// Reactive data
const filteredSpecializations = ref<Specialization[]>([]);
const filteredCurriculumVersions = ref<CurriculumVersion[]>([]);

// Computed properties
const isSubmitting = computed(() => inertiaForm.processing);

// Watch program selection to filter related data
const onProgramChange = (programId: string) => {
  // Filter specializations by program
  filteredSpecializations.value = props.specializations.filter(
    spec => spec.program_id === parseInt(programId)
  );
  
  // Filter curriculum versions by program
  filteredCurriculumVersions.value = props.curriculumVersions.filter(
    cv => cv.program_id === parseInt(programId)
  );
  
  // Reset dependent fields
  inertiaForm.specialization_id = '';
  inertiaForm.curriculum_version_id = '';
};

// Form submission handler
const onSubmit = async (values: any) => {
  // Convert string IDs to numbers for API
  const formData = {
    ...values,
    program_id: parseInt(values.program_id),
    specialization_id: values.specialization_id ? parseInt(values.specialization_id) : undefined,
    curriculum_version_id: parseInt(values.curriculum_version_id),
  };

  Object.assign(inertiaForm, formData);
  
  inertiaForm.post(studentRoutes.store(), {
    onSuccess: () => {
      // Redirect to students index with success message
      window.location.href = route('students.index');
    },
    onError: (errors) => {
      console.error('Student creation failed:', errors);
    },
  });
};

// Initialize form
onMounted(() => {
  // Set initial filters if there's only one program
  if (props.programs.length === 1) {
    inertiaForm.program_id = props.programs[0].id.toString();
    onProgramChange(inertiaForm.program_id);
  }
});
</script>

<template>
  <Head title="Create Student" />
  
  <div class="max-w-2xl mx-auto py-8">
    <Card>
      <CardHeader>
        <CardTitle>Create New Student</CardTitle>
        <CardDescription>
          Add a new student to the system. Students are automatically admitted upon creation.
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <Form
          :validation-schema="createStudentSchema"
          @submit="onSubmit"
          class="space-y-6"
        >
          <!-- Full Name -->
          <FormField v-slot="{ componentField }" name="full_name">
            <FormItem>
              <FormLabel>Full Name *</FormLabel>
              <FormControl>
                <Input
                  v-bind="componentField"
                  placeholder="Enter student's full name"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          </FormField>

          <!-- Email -->
          <FormField v-slot="{ componentField }" name="email">
            <FormItem>
              <FormLabel>Email Address *</FormLabel>
              <FormControl>
                <Input
                  v-bind="componentField"
                  type="email"
                  placeholder="<EMAIL>"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          </FormField>

          <!-- Phone (Optional) -->
          <FormField v-slot="{ componentField }" name="phone">
            <FormItem>
              <FormLabel>Phone Number</FormLabel>
              <FormControl>
                <Input
                  v-bind="componentField"
                  placeholder="Optional phone number"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          </FormField>

          <!-- Program -->
          <FormField v-slot="{ componentField }" name="program_id">
            <FormItem>
              <FormLabel>Program *</FormLabel>
              <Select @update:model-value="onProgramChange">
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a program" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem
                    v-for="program in programs"
                    :key="program.id"
                    :value="program.id.toString()"
                  >
                    {{ program.name }} ({{ program.code }})
                  </SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          </FormField>

          <!-- Specialization (Optional) -->
          <FormField v-slot="{ componentField }" name="specialization_id">
            <FormItem>
              <FormLabel>Specialization</FormLabel>
              <Select :disabled="!inertiaForm.program_id">
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select specialization (optional)" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="">No specialization</SelectItem>
                  <SelectItem
                    v-for="spec in filteredSpecializations"
                    :key="spec.id"
                    :value="spec.id.toString()"
                  >
                    {{ spec.name }}
                  </SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          </FormField>

          <!-- Curriculum Version -->
          <FormField v-slot="{ componentField }" name="curriculum_version_id">
            <FormItem>
              <FormLabel>Curriculum Version *</FormLabel>
              <Select :disabled="!inertiaForm.program_id">
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select curriculum version" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem
                    v-for="cv in filteredCurriculumVersions"
                    :key="cv.id"
                    :value="cv.id.toString()"
                  >
                    {{ cv.name }} (v{{ cv.version }})
                  </SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          </FormField>

          <!-- Admission Date -->
          <FormField v-slot="{ componentField }" name="admission_date">
            <FormItem>
              <FormLabel>Admission Date *</FormLabel>
              <FormControl>
                <Input
                  v-bind="componentField"
                  type="date"
                  :max="format(new Date(), 'yyyy-MM-dd')"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          </FormField>

          <!-- Expected Graduation Date (Optional) -->
          <FormField v-slot="{ componentField }" name="expected_graduation_date">
            <FormItem>
              <FormLabel>Expected Graduation Date</FormLabel>
              <FormControl>
                <Input
                  v-bind="componentField"
                  type="date"
                  :min="inertiaForm.admission_date"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          </FormField>

          <!-- Notes (Optional) -->
          <FormField v-slot="{ componentField }" name="notes">
            <FormItem>
              <FormLabel>Admission Notes</FormLabel>
              <FormControl>
                <Textarea
                  v-bind="componentField"
                  placeholder="Optional notes about the student admission..."
                  rows="3"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          </FormField>

          <!-- Form Errors -->
          <div
            v-if="inertiaForm.hasErrors"
            class="bg-red-50 border border-red-200 rounded-md p-3"
          >
            <ul class="text-sm text-red-600 space-y-1">
              <li v-for="(error, field) in inertiaForm.errors" :key="field">
                {{ error }}
              </li>
            </ul>
          </div>

          <!-- Form Actions -->
          <div class="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              @click="$inertia.visit(route('students.index'))"
              :disabled="isSubmitting"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              :disabled="isSubmitting"
            >
              <span v-if="isSubmitting">Creating Student...</span>
              <span v-else>Create Student</span>
            </Button>
          </div>
        </Form>
      </CardContent>
    </Card>
  </div>
</template>
```

---

## 🔄 Prompt 9: Update Students Index Page

Update the existing `resources/js/pages/students/Index.vue` to include campus filtering and remove admission workflow:

```vue
<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { Head } from '@inertiajs/vue3';
import { useApi } from '@/composables/useApiRequest';

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

import type { Student, Program, StudentStats } from '@/types/models';
import { studentRoutes } from '@/utils/routes';

interface Props {
  programs: Program[];
}

const props = defineProps<Props>();
const api = useApi();

// Reactive data
const students = ref<Student[]>([]);
const stats = ref<StudentStats | null>(null);
const loading = ref(true);
const searchQuery = ref('');
const statusFilter = ref('');
const programFilter = ref('');

// Computed properties
const filteredStudents = computed(() => {
  let result = students.value;
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(student => 
      student.full_name.toLowerCase().includes(query) ||
      student.student_id.toLowerCase().includes(query) ||
      student.email.toLowerCase().includes(query)
    );
  }
  
  if (statusFilter.value) {
    result = result.filter(student => student.status === statusFilter.value);
  }
  
  if (programFilter.value) {
    result = result.filter(student => student.program_id === parseInt(programFilter.value));
  }
  
  return result;
});

const statusOptions = [
  { value: '', label: 'All Status' },
  { value: 'admitted', label: 'Admitted' },
  { value: 'active', label: 'Active' },
  { value: 'inactive', label: 'Inactive' },
  { value: 'graduated', label: 'Graduated' },
  { value: 'dropped_out', label: 'Dropped Out' },
];

const getStatusVariant = (status: string) => {
  switch (status) {
    case 'admitted': return 'default';
    case 'active': return 'default';
    case 'inactive': return 'secondary';
    case 'graduated': return 'outline';
    case 'dropped_out': return 'destructive';
    default: return 'secondary';
  }
};

// Methods
const fetchStudents = async () => {
  try {
    loading.value = true;
    const response = await api.get(studentRoutes.index(), {
      search: searchQuery.value,
      status: statusFilter.value,
      program_id: programFilter.value,
    });
    
    if (response.data.value.success) {
      students.value = response.data.value.data.students;
    }
  } catch (error) {
    console.error('Failed to fetch students:', error);
  } finally {
    loading.value = false;
  }
};

const fetchStats = async () => {
  try {
    const response = await api.get(studentRoutes.stats());
    if (response.data.value.success) {
      stats.value = response.data.value.data;
    }
  } catch (error) {
    console.error('Failed to fetch stats:', error);
  }
};

const onSearch = () => {
  fetchStudents();
};

const onFilterChange = () => {
  fetchStudents();
};

const navigateToCreate = () => {
  window.location.href = route('students.create');
};

// Lifecycle
onMounted(() => {
  fetchStudents();
  fetchStats();
});
</script>

<template>
  <Head title="Students" />
  
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-3xl font-bold">Students</h1>
        <p class="text-muted-foreground">
          Manage admitted students for the current campus
        </p>
      </div>
      <Button @click="navigateToCreate">
        Create Student
      </Button>
    </div>

    <!-- Statistics Cards -->
    <div v-if="stats" class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <Card>
        <CardHeader class="pb-2">
          <CardTitle class="text-sm font-medium">Total Students</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ stats.total }}</div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader class="pb-2">
          <CardTitle class="text-sm font-medium">Admitted</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ stats.by_status.admitted || 0 }}</div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader class="pb-2">
          <CardTitle class="text-sm font-medium">Active</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ stats.by_status.active || 0 }}</div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader class="pb-2">
          <CardTitle class="text-sm font-medium">Graduated</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ stats.by_status.graduated || 0 }}</div>
        </CardContent>
      </Card>
    </div>

    <!-- Filters -->
    <Card>
      <CardHeader>
        <CardTitle>Filters</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <!-- Search -->
          <div>
            <Input
              v-model="searchQuery"
              placeholder="Search by name, ID, or email..."
              @keyup.enter="onSearch"
            />
          </div>

          <!-- Status Filter -->
          <div>
            <Select v-model="statusFilter" @update:model-value="onFilterChange">
              <SelectTrigger>
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem
                  v-for="option in statusOptions"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <!-- Program Filter -->
          <div>
            <Select v-model="programFilter" @update:model-value="onFilterChange">
              <SelectTrigger>
                <SelectValue placeholder="Filter by program" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Programs</SelectItem>
                <SelectItem
                  v-for="program in programs"
                  :key="program.id"
                  :value="program.id.toString()"
                >
                  {{ program.name }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <!-- Search Button -->
          <div>
            <Button @click="onSearch" class="w-full">
              Search
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Students Table -->
    <Card>
      <CardHeader>
        <CardTitle>Students List</CardTitle>
      </CardHeader>
      <CardContent>
        <div v-if="loading" class="text-center py-8">
          Loading students...
        </div>
        
        <div v-else-if="filteredStudents.length === 0" class="text-center py-8">
          <p class="text-muted-foreground">No students found</p>
        </div>
        
        <Table v-else>
          <TableHeader>
            <TableRow>
              <TableHead>Student ID</TableHead>
              <TableHead>Full Name</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Program</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Admission Date</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow v-for="student in filteredStudents" :key="student.id">
              <TableCell class="font-medium">
                {{ student.student_id }}
              </TableCell>
              <TableCell>
                {{ student.full_name }}
              </TableCell>
              <TableCell>
                {{ student.email }}
              </TableCell>
              <TableCell>
                {{ student.program?.name }}
              </TableCell>
              <TableCell>
                <Badge :variant="getStatusVariant(student.status)">
                  {{ student.status_label }}
                </Badge>
              </TableCell>
              <TableCell>
                {{ student.admission_date }}
              </TableCell>
              <TableCell>
                <div class="flex space-x-2">
                  <Button size="sm" variant="outline">
                    View
                  </Button>
                  <Button size="sm" variant="outline">
                    Edit
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  </div>
</template>
```

---

## 🎯 Prompt 10: Add Web Routes

Add to `routes/web.php`:

```php
// Student management routes
Route::group(['prefix' => 'students', 'middleware' => ['auth']], function () {
    Route::get('/', [App\Http\Controllers\Web\StudentController::class, 'index'])
        ->name('students.index');
    Route::get('/create', [App\Http\Controllers\Web\StudentController::class, 'create'])
        ->name('students.create');
    Route::get('/{student}', [App\Http\Controllers\Web\StudentController::class, 'show'])
        ->name('students.show');
    Route::get('/{student}/edit', [App\Http\Controllers\Web\StudentController::class, 'edit'])
        ->name('students.edit');
});
```

And create the corresponding Web controller methods:

```php
/**
 * Display students for current campus
 */
public function index()
{
    $campusId = session()->get('current_campus_id');
    
    if (!$campusId) {
        return redirect()->route('select-campus.index')
            ->with('error', 'Please select a campus first');
    }

    $programs = Program::where('campus_id', $campusId)->get();
    
    return Inertia::render('Students/Index', [
        'programs' => ProgramResource::collection($programs),
    ]);
}

/**
 * Show student creation form
 */
public function create()
{
    $campusId = session()->get('current_campus_id');
    
    if (!$campusId) {
        return redirect()->route('select-campus.index')
            ->with('error', 'Please select a campus first');
    }

    $programs = Program::where('campus_id', $campusId)->get();
    $specializations = Specialization::whereIn('program_id', $programs->pluck('id'))->get();
    $curriculumVersions = CurriculumVersion::whereIn('program_id', $programs->pluck('id'))->get();
    
    return Inertia::render('Students/Create', [
        'programs' => ProgramResource::collection($programs),
        'specializations' => SpecializationResource::collection($specializations),
        'curriculumVersions' => CurriculumVersionResource::collection($curriculumVersions),
    ]);
}
```

---

## 📋 Complete Implementation Checklist

### Backend
- [ ] Extend `StudentService` with `createAdmittedStudent()` and `getStudentsByCampus()` methods
- [ ] Update `StoreStudentRequest` with new validation rules and campus handling
- [ ] Update API `StudentController` with campus-filtered listing and unified creation
- [ ] Update `StudentResource` to include all necessary fields
- [ ] Add student routes to `routes/api.php`
- [ ] Test endpoints: `GET /api/students` and `POST /api/students`

### Frontend
- [ ] Add TypeScript types to `resources/js/types/models.ts`
- [ ] Update route utilities in `resources/js/utils/routes.ts`
- [ ] Create `Students/Create.vue` component with complete form
- [ ] Update `students/Index.vue` with campus filtering and remove admission workflow
- [ ] Add web routes for student pages
- [ ] Test the complete student creation workflow

## 🔍 Testing Examples

### Backend API Test
```bash
# Test student creation endpoint
curl -X POST "http://your-app.test/api/students" \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "full_name": "John Doe",
    "email": "<EMAIL>",
    "program_id": 1,
    "curriculum_version_id": 1,
    "admission_date": "2024-01-15",
    "notes": "Excellent academic record"
  }'
```

### Frontend Testing Flow
1. Navigate to `/students` page (shows only current campus students)
2. Click "Create Student" button
3. Fill in required fields: full name, email, program, curriculum version, admission date
4. Submit form and verify student is created with "admitted" status
5. Verify student appears in the filtered list
6. Check that student role is automatically assigned

