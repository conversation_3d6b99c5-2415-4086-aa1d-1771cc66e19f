# 🎓 Comprehensive Academic Seeder Workflow
## Quy Trình Tạo Data Seeder Tái Hiện Hoàn Chỉnh Quá Trình <PERSON>ọ<PERSON> Tập Của Sinh Viên

### 📋 **Tổng Quan**

Document này mô tả quy trình hoàn chỉnh để tạo các data seeder nhằm mô phỏng toàn bộ quá trình học tập của sinh viên trong một kỳ học, từ đăng ký môn học đến tính toán GPA cuối kỳ.

---

## 🎯 **Các Bước Tạo Data Seeder Hoàn Chỉnh**

### **Bước 1: Cơ sở hạ tầng giáo dục** 📚
**Flow:** `campuses → users → roles → permissions → campus_user_roles`

**Bảng liên quan:**
- `campuses` - Campus locations *(Volume: 3-12 campuses)*
- `users` - Admin users, system users *(Volume: 50-200 users)*
- `roles` - User roles (admin, staff, etc.) *(Volume: 8-15 roles)*
- `permissions` - System permissions *(Volume: 100-300 permissions)*
- `campus_user_roles` - User role assignments *(Volume: 100-500 assignments)*

**M<PERSON><PERSON> đích:** Thiết lập hệ thống cơ bản với campus, admin users, và phân quyền

**Seeder:** `UserSeeder.php`

**Data Volume Example:**
```php
// Recommended volumes cho pagination testing
$campusCount = $this->getVolume('campuses', 5);
$userCount = $campusCount * 20; // 20 users per campus
$roleCount = 12; // Fixed number of roles
```

---

### **Bước 2: Cấu trúc học thuật cơ bản** 🏫
**Flow:** `programs → specializations → units → semesters`

**Bảng liên quan:**
- `programs` - Degree programs *(Volume: 8-40 programs)*
- `specializations` - Program specializations *(Volume: 15-100 specializations)*
- `units` - Individual courses/subjects *(Volume: 50-500 units)*
- `semesters` - Academic periods *(Volume: 8-12 semesters)*

**Mục đích:** Định nghĩa các chương trình học, chuyên ngành, môn học và kỳ học

**Seeders:** `ProgramSeeder.php`, `UnitSeeder.php`, `SemesterSeeder.php`

**Factory Usage:**
```php
// Tạo programs với factories
Program::factory($this->getVolume('programs', 15))->create();

// Tạo units với realistic distribution
Unit::factory($this->getVolume('units', 120))
    ->state(function () {
        return [
            'credit_hours' => fake()->randomElement([3, 4, 6]),
            'difficulty_level' => fake()->randomElement(['beginner', 'intermediate', 'advanced']),
        ];
    })
    ->create();
```

---

### **Bước 3: Giảng viên và năng lực giảng dạy** 👨‍🏫
**Flow:** `lectures`

**Bảng liên quan:**
- `lectures` - Lecturer profiles *(Volume: 25-300 lecturers)*

**Mục đích:** Tạo profile giảng viên với chuyên môn và lịch giảng dạy ưa thích

**Seeder:** `LectureSeeder.php`

**High-Volume Generation:**
```php
// Tạo lecturers với specializations
Lecture::factory($this->getVolume('lectures', 60))
    ->create()
    ->each(function ($lecture) {
        // Assign 2-4 specialization areas per lecturer
        $specializations = Specialization::inRandomOrder()
            ->limit(fake()->numberBetween(2, 4))
            ->pluck('id');
        
        $lecture->specializations()->attach($specializations);
    });
```

---

### **Bước 4: Chương trình đào tạo** 📋
**Flow:** `curriculum_versions → curriculum_units → unit_prerequisite_groups → unit_prerequisite_conditions`

**Bảng liên quan:**
- `curriculum_versions` - Curriculum versions for programs
- `curriculum_units` - Units trong curriculum với year level
- `unit_prerequisite_groups` - Prerequisite group definitions
- `unit_prerequisite_conditions` - Specific prerequisite rules

**Mục đích:** Thiết lập cấu trúc curriculum và yêu cầu tiên quyết

**Seeder:** `CurriculumSeeder.php`

---

### **Bước 5: Sinh viên và đăng ký** 🎓
**Flow:** `students → course_offerings → course_registrations → enrollments`

**Bảng liên quan:**
- `students` - Student records
- `course_offerings` - Course instances for semester
- `course_registrations` - Student registrations for courses
- `enrollments` - Enrollment records

**Mục đích:** Tạo sinh viên và đăng ký vào các môn học

**Seeders:** `CurriculumSeeder.php` (students), `CourseOfferingSeeder.php`, `CourseRegistrationSeeder.php`

---

### **Bước 6: Cấu trúc đánh giá** 📝
**Flow:** `syllabus → assessment_components → assessment_component_details`

**Bảng liên quan:**
- `syllabus` - Course syllabi
- `assessment_components` - Assessment types (exam, assignment, project)
- `assessment_component_details` - Detailed breakdown of assessments

**Mục đích:** Thiết lập syllabus và cấu trúc đánh giá chi tiết cho từng môn

**Seeder:** `SyllabusSeeder.php`

---

### **Bước 7: Lịch học và phòng học** 🏛️
**Flow:** `rooms → room_bookings → class_sessions`

**Bảng liên quan:**
- `rooms` - Available classrooms
- `room_bookings` - Room reservation system (polymorphic)
- `class_sessions` - Scheduled class sessions

**Mục đích:** Tạo schedule buổi học với phòng học và giảng viên

**Seeders:** `RoomSeeder.php`, `ClassScheduleSeeder.php`

---

### **Bước 8: Nhóm học tập** 👥
**Flow:** `student_groups → student_group_members`

**Bảng liên quan:**
- `student_groups` - Study/project groups
- `student_group_members` - Group membership

**Mục đích:** Tạo các nhóm học tập cho project work và group assignments

**Seeder:** `StudentGroupSeeder.php`

---

### **Bước 9: Quá trình điểm danh** ✅
**Flow:** `attendances`

**Bảng liên quan:**
- `attendances` - Attendance records cho từng class session

**Mục đích:** Record attendance cho từng buổi học với status, timing, method

**Seeder:** `AttendanceSeeder.php`

---

### **Bước 10: Quá trình đánh giá và chấm điểm** 📊
**Flow:** `assessment_component_detail_scores`

**Bảng liên quan:**
- `assessment_component_detail_scores` - Individual scores for assessment components

**Mục đích:** Record scores cho từng assignment/exam/project component với detailed tracking

**Seeder:** `AssessmentScoreSeeder.php`

---

### **Bước 11: Kết quả học tập cuối kỳ** 🏆
**Flow:** `academic_records`

**Bảng liên quan:**
- `academic_records` - Final course records với grades, completion status

**Mục đích:** Tính toán và lưu final grades, completion status, credit hours earned

**Seeder:** `AcademicRecordSeeder.php`

---

### **Bước 12: Tính toán GPA và thành tích** 📈
**Flow:** `gpa_calculations`

**Bảng liên quan:**
- `gpa_calculations` - GPA calculations (semester, cumulative, program-specific)

**Mục đích:** Tính semester GPA, cumulative GPA, academic standing, rankings

**Seeder:** `GPACalculationSeeder.php`

---

### **Bước 13: Xử lý vấn đề học tập** ⚠️
**Flow:** `academic_holds`

**Bảng liên quan:**
- `academic_holds` - Academic holds và restrictions

**Mục đích:** Manage holds (financial, academic probation, prerequisite violations)

**Seeder:** `AcademicHoldSeeder.php`

---

## 🔄 **Thứ Tự Chạy Seeders Được Đề Xuất**

```php
// database/seeders/ComprehensiveAcademicWorkflowSeeder.php
<?php

declare(strict_types=1);

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class ComprehensiveAcademicWorkflowSeeder extends BaseVolumeSeeder
{
    /**
     * Comprehensive academic workflow seeder mô phỏng 
     * một complete student learning process trong một semester.
     * 
     * Sử dụng: php artisan db:seed --class=ComprehensiveAcademicWorkflowSeeder
     * Với volume: SEEDER_VOLUME=large php artisan db:seed --class=ComprehensiveAcademicWorkflowSeeder
     */
    public function run(): void
    {
        // Set volume từ environment variable
        $volume = env('SEEDER_VOLUME', 'medium');
        $this->setVolumeSize($volume);
        
        $this->command->info("🎓 Starting Comprehensive Academic Workflow Seeding [{$this->volumeSize}]...");
        
        // Hiển thị expected data volumes
        $this->displayExpectedVolumes();

        $this->call([
            // === 0. ĐIỀU KIỆN TIÊN QUYẾT (nếu models chưa tồn tại) ===
            // Chỉ chạy nếu models cần được generate:
            // php artisan make:model Room -mf
            // php artisan make:model ClassSession -mf
            // php artisan make:model Attendance -mf
            // php artisan make:model StudentGroup -mf
            // php artisan make:model StudentGroupMember -mf
            // php artisan make:model AcademicRecord -mf
            // php artisan make:model AssessmentComponentDetailScore -mf
            // php artisan make:model GpaCalculation -mf
            // php artisan make:model RoomBooking -mf
            
            // === 1. FOUNDATION SETUP ===
            UserSeeder::class,              // campuses, users, roles, permissions
            LectureSeeder::class,           // lecturer profiles với expertise
            
            // === 2. ACADEMIC STRUCTURE ===
            ProgramSeeder::class,           // programs và specializations
            UnitSeeder::class,              // available units/courses
            SemesterSeeder::class,          // academic periods
            CurriculumSeeder::class,        // curriculum structure + students
            
            // === 3. ASSESSMENT FRAMEWORK ===
            SyllabusSeeder::class,          // syllabus với assessment structure
            
            // === 4. COURSE DELIVERY SETUP ===
            CourseOfferingSeeder::class,    // course instances cho semester
            CourseRegistrationSeeder::class, // student course registrations
            RoomSeeder::class,              // classroom facilities
            ClassScheduleSeeder::class,     // room bookings + class sessions
            
            // === 5. LEARNING PROCESS SIMULATION ===
            StudentGroupSeeder::class,      // study groups cho collaborative work
            AttendanceSeeder::class,        // attendance tracking across semester
            
            // === 6. ASSESSMENT & GRADING ===
            AssessmentScoreSeeder::class,   // detailed component scoring
            
            // === 7. ACADEMIC OUTCOMES ===
            AcademicRecordSeeder::class,    // final course grades và completion
            GPACalculationSeeder::class,    // GPA calculations và rankings
            AcademicHoldSeeder::class,      // holds và academic interventions
        ]);

        $this->command->info('✅ Comprehensive Academic Workflow Seeding Completed!');
        $this->printSummaryStats();
        $this->printPaginationInfo();
    }

    private function displayExpectedVolumes(): void
    {
        $this->command->info('📊 Expected Data Volumes:');
        $this->command->line('   🏫 Campuses: ' . $this->getVolume('campuses', 5));
        $this->command->line('   📚 Programs: ' . $this->getVolume('programs', 15));
        $this->command->line('   🎯 Specializations: ' . $this->getVolume('specializations', 35));
        $this->command->line('   📖 Units: ' . $this->getVolume('units', 120));
        $this->command->line('   🎓 Students: ' . $this->getVolume('students', 500));
        $this->command->line('   👨‍🏫 Lecturers: ' . $this->getVolume('lectures', 60));
        $this->command->line('   📅 Course Offerings: ' . $this->getVolume('course_offerings', 100));
        $this->command->line('   🏛️ Rooms: ' . ($this->getVolume('campuses', 5) * 20));
        $this->command->line('   👥 Student Groups: ' . $this->getVolume('student_groups', 80));
        $this->command->line('');
        $this->command->line('Thời gian seeding dự kiến: ' . $this->estimatedTime() . ' phút');
        $this->command->line('');
    }

    private function estimatedTime(): string
    {
        $volumes = [
            'small' => '2-3',
            'medium' => '5-8', 
            'large' => '15-25',
            'xlarge' => '30-45'
        ];
        
        return $volumes[$this->volumeSize] ?? '5-8';
    }

    private function printSummaryStats(): void
    {
        $this->command->info('📊 Thống Kê Cuối Cùng:');
        $this->command->line('   🎓 Students: ' . number_format(\App\Models\Student::count()));
        $this->command->line('   📅 Course Offerings: ' . number_format(\App\Models\CourseOffering::count()));
        $this->command->line('   📝 Course Registrations: ' . number_format(\App\Models\CourseRegistration::count()));
        $this->command->line('   🏛️ Class Sessions: ' . number_format(\App\Models\ClassSession::count()));
        $this->command->line('   ✅ Attendance Records: ' . number_format(\App\Models\Attendance::count()));
        $this->command->line('   📊 Assessment Scores: ' . number_format(\App\Models\AssessmentComponentDetailScore::count()));
        $this->command->line('   🏆 Academic Records: ' . number_format(\App\Models\AcademicRecord::count()));
        $this->command->line('   📈 GPA Calculations: ' . number_format(\App\Models\GpaCalculation::count()));
        $this->command->line('   ⚠️ Academic Holds: ' . number_format(\App\Models\AcademicHold::count()));
        $this->command->line('   👥 Student Groups: ' . number_format(\App\Models\StudentGroup::count()));
    }

    private function printPaginationInfo(): void
    {
        $this->command->info('📄 Thông Tin Pagination Testing:');
        
        $studentsPerPage = config('seeder.pagination_config.students_per_page', 15);
        $totalStudents = \App\Models\Student::count();
        $studentPages = ceil($totalStudents / $studentsPerPage);
        
        $offeringsPerPage = config('seeder.pagination_config.course_offerings_per_page', 20);
        $totalOfferings = \App\Models\CourseOffering::count();
        $offeringPages = ceil($totalOfferings / $offeringsPerPage);
        
        $this->command->line("   📚 Students: {$totalStudents} records = {$studentPages} pages ({$studentsPerPage}/page)");
        $this->command->line("   📅 Course Offerings: {$totalOfferings} records = {$offeringPages} pages ({$offeringsPerPage}/page)");
        
        if ($totalStudents >= 100) {
            $this->command->line('   ✅ Đủ dữ liệu cho pagination testing!');
        } else {
            $this->command->line('   ⚠️ Nên sử dụng volume lớn hơn cho pagination testing');
        }
    }
}
```

---

## 📅 **Timeline Thực Tế Của Một Kỳ Học**

### **Pre-Semester (Tuần -2 đến -1)**
1. **Course Planning**: Tạo course offerings, assign instructors
2. **Room Allocation**: Book classrooms và facilities
3. **Student Registration**: Students đăng ký courses
4. **Group Formation**: Tạo study groups cho group projects

### **Early Semester (Tuần 1-4)**
1. **Class Commencement**: Regular class sessions start
2. **Attendance Tracking**: Daily attendance recording
3. **Early Assessments**: Small quizzes, participation grades
4. **Group Projects**: Group formation và initial work

### **Mid-Semester (Tuần 5-8)**
1. **Midterm Period**: Midterm exams và major assessments
2. **Score Recording**: Record midterm scores
3. **Academic Interventions**: Identify at-risk students
4. **Project Milestones**: Major project deliverables

### **Late Semester (Tuần 9-12)**
1. **Final Preparations**: Review sessions, final projects
2. **Final Assessments**: Final exams, project presentations
3. **Score Compilation**: All assessment scores recorded
4. **Attendance Summary**: Final attendance calculations

### **Post-Semester (Tuần 13-14)**
1. **Grade Calculation**: Compile final course grades
2. **Academic Records**: Create academic records
3. **GPA Calculation**: Semester và cumulative GPA
4. **Academic Standing**: Determine probation, honors, etc.

---

## 🏗️ **Cấu Trúc Seeder Mẫu**

### **AttendanceSeeder.php Example**
```php
<?php

namespace Database\Seeders;

use App\Models\ClassSession;
use App\Models\Student;
use App\Models\Attendance;
use App\Models\CourseRegistration;
use Illuminate\Database\Seeder;

class AttendanceSeeder extends Seeder
{
    public function run(): void
    {
        $this->command->info('📝 Creating attendance records...');

        $classSessions = ClassSession::with(['courseOffering.courseRegistrations.student'])->get();

        foreach ($classSessions as $session) {
            $registrations = $session->courseOffering->courseRegistrations;
            
            foreach ($registrations as $registration) {
                $this->createAttendanceRecord($session, $registration->student);
            }
        }
    }

    private function createAttendanceRecord(ClassSession $session, Student $student): void
    {
        // Simulate realistic attendance patterns
        $attendanceRate = 0.85; // 85% overall attendance rate
        $isPresent = fake()->boolean($attendanceRate * 100);

        $status = $isPresent ? 'present' : fake()->randomElement(['absent', 'late', 'excused']);
        
        if ($status === 'late') {
            $minutesLate = fake()->numberBetween(5, 30);
            $checkInTime = $session->start_time->addMinutes($minutesLate);
        } else {
            $minutesLate = 0;
            $checkInTime = $isPresent ? $session->start_time->addMinutes(fake()->numberBetween(-5, 10)) : null;
        }

        Attendance::create([
            'class_session_id' => $session->id,
            'student_id' => $student->id,
            'recorded_by_lecture_id' => $session->instructor_id,
            'status' => $status,
            'check_in_time' => $checkInTime,
            'check_out_time' => $isPresent ? $session->end_time->subMinutes(fake()->numberBetween(0, 15)) : null,
            'minutes_late' => $minutesLate,
            'recording_method' => fake()->randomElement(['manual', 'qr_code', 'mobile_app']),
            'participation_level' => $isPresent ? fake()->randomElement(['excellent', 'good', 'average']) : null,
            'participation_score' => $isPresent ? fake()->numberBetween(6, 10) : null,
            'is_verified' => true,
            'affects_grade' => true,
            'verified_at' => now(),
            'verified_by_lecture_id' => $session->instructor_id,
        ]);
    }
}
```

### **AssessmentScoreSeeder.php Example**
```php
<?php

namespace Database\Seeders;

use App\Models\AssessmentComponentDetail;
use App\Models\Student;
use App\Models\AssessmentComponentDetailScore;
use App\Models\CourseRegistration;
use Illuminate\Database\Seeder;

class AssessmentScoreSeeder extends Seeder
{
    public function run(): void
    {
        $this->command->info('📊 Creating assessment scores...');

        $assessmentDetails = AssessmentComponentDetail::with([
            'component.syllabus.courseOfferings.courseRegistrations.student'
        ])->get();

        foreach ($assessmentDetails as $detail) {
            foreach ($detail->component->syllabus->courseOfferings as $offering) {
                foreach ($offering->courseRegistrations as $registration) {
                    $this->createAssessmentScore($detail, $registration->student, $offering);
                }
            }
        }
    }

    private function createAssessmentScore($detail, $student, $courseOffering): void
    {
        // Simulate realistic grade distribution
        $gradeDistribution = [
            'A' => ['min' => 85, 'max' => 100, 'weight' => 15],
            'B' => ['min' => 75, 'max' => 84, 'weight' => 35],
            'C' => ['min' => 65, 'max' => 74, 'weight' => 30],
            'D' => ['min' => 50, 'max' => 64, 'weight' => 15],
            'F' => ['min' => 0, 'max' => 49, 'weight' => 5],
        ];

        $selectedGrade = $this->selectGradeByWeight($gradeDistribution);
        $percentage = fake()->numberBetween($selectedGrade['min'], $selectedGrade['max']);
        
        AssessmentComponentDetailScore::create([
            'assessment_component_detail_id' => $detail->id,
            'student_id' => $student->id,
            'course_offering_id' => $courseOffering->id,
            'graded_by_lecture_id' => $courseOffering->instructor_id,
            'points_earned' => ($percentage / 100) * $detail->max_points,
            'percentage_score' => $percentage,
            'letter_grade' => array_search($selectedGrade, $gradeDistribution),
            'gpa_points' => $this->calculateGPAPoints($percentage),
            'submitted_at' => fake()->dateTimeBetween('-2 weeks', '-1 week'),
            'graded_at' => fake()->dateTimeBetween('-1 week', 'now'),
            'status' => 'graded',
            'score_status' => 'final',
            'instructor_feedback' => $this->generateFeedback($percentage),
            'is_verified' => true,
        ]);
    }

    private function selectGradeByWeight(array $distribution): array
    {
        $totalWeight = array_sum(array_column($distribution, 'weight'));
        $random = fake()->numberBetween(1, $totalWeight);
        
        $currentWeight = 0;
        foreach ($distribution as $grade => $data) {
            $currentWeight += $data['weight'];
            if ($random <= $currentWeight) {
                return $data;
            }
        }
        
        return end($distribution);
    }

    private function generateFeedback(float $percentage): string
    {
        if ($percentage >= 85) {
            return 'Excellent work! Shows deep understanding of the concepts.';
        } elseif ($percentage >= 75) {
            return 'Good work. Minor improvements needed in some areas.';
        } elseif ($percentage >= 65) {
            return 'Satisfactory work. Consider reviewing key concepts.';
        } elseif ($percentage >= 50) {
            return 'Needs improvement. Please seek additional help.';
        } else {
            return 'Unsatisfactory. Requires significant additional work.';
        }
    }

    private function calculateGPAPoints(float $percentage): float
    {
        if ($percentage >= 85) return 4.0;
        if ($percentage >= 75) return 3.0;
        if ($percentage >= 65) return 2.0;
        if ($percentage >= 50) return 1.0;
        return 0.0;
    }
}
```

---

## 🎯 **Key Features của Comprehensive Seeder**

### **1. Realistic Data Distribution**
- Grade distributions mirror real academic performance
- Attendance patterns reflect typical student behavior
- Assessment timing follows semester progression

### **2. Referential Integrity**
- All foreign key relationships properly maintained
- Cascade dependencies handled correctly
- Data consistency across all tables

### **3. Configurable Parameters**
- Adjustable student counts
- Flexible semester timelines
- Customizable assessment structures

### **4. Complete Workflow Simulation**
- End-to-end semester simulation
- Multiple assessment types
- Comprehensive attendance tracking
- Final grade calculations

### **5. Academic Realism**
- Prerequisites enforced
- Academic calendar respected
- Realistic instructor assignments
- Proper room scheduling

---

## 🚀 **Sử Dụng**

### **Bước 0: Điều Kiện Tiên Quyết - Tạo Models & Factories**
```bash
# Generate tất cả required models với migrations và factories
php artisan make:model Room -mf
php artisan make:model ClassSession -mf
php artisan make:model Attendance -mf
php artisan make:model StudentGroup -mf
php artisan make:model StudentGroupMember -mf
php artisan make:model AcademicRecord -mf
php artisan make:model AssessmentComponentDetailScore -mf
php artisan make:model GpaCalculation -mf
php artisan make:model RoomBooking -mf

# Tạo configuration file
cp config/app.php config/seeder.php
# Edit config/seeder.php với volume configurations ở trên
```

### **Bước 1: Tạo Base Seeder Class**
```bash
# Tạo base volume seeder
touch database/seeders/BaseVolumeSeeder.php
# Copy BaseVolumeSeeder code từ trên
```

### **Bước 2: Chạy Complete Workflow với Different Volumes**

**Small Dataset (Development)**
```bash
# Quick development testing (150 students, ~3 phút)
SEEDER_VOLUME=small php artisan db:seed --class=ComprehensiveAcademicWorkflowSeeder
```

**Medium Dataset (Default)**
```bash
# Balanced testing environment (500 students, ~8 phút)
php artisan db:seed --class=ComprehensiveAcademicWorkflowSeeder
# HOẶC
SEEDER_VOLUME=medium php artisan db:seed --class=ComprehensiveAcademicWorkflowSeeder
```

**Large Dataset (Production Simulation)**
```bash
# Production-like data (2000 students, ~25 phút)
SEEDER_VOLUME=large php artisan db:seed --class=ComprehensiveAcademicWorkflowSeeder
```

**Extra Large Dataset (Performance Testing)**
```bash
# Heavy load testing (5000 students, ~45 phút)
SEEDER_VOLUME=xlarge php artisan db:seed --class=ComprehensiveAcademicWorkflowSeeder
```

### **Bước 3: Chạy Individual Seeders với Volume Control**
```bash
# Individual seeders với volume awareness
php artisan db:seed --class=AttendanceSeeder
php artisan db:seed --class=AssessmentScoreSeeder
php artisan db:seed --class=StudentGroupSeeder

# Với specific volume
SEEDER_VOLUME=large php artisan db:seed --class=AttendanceSeeder
```

### **Bước 4: Factory Testing**
```bash
# Test individual factories
php artisan tinker --execute="
// Test student factory với different states
\App\Models\Student::factory()->active()->count(10)->create();
\App\Models\Student::factory()->graduated()->count(5)->create();
\App\Models\Student::factory()->newStudent()->count(20)->create();

// Test attendance factory với states
\App\Models\Attendance::factory()->present()->count(50)->create();
\App\Models\Attendance::factory()->absent()->count(10)->create();
\App\Models\Attendance::factory()->late()->count(15)->create();
"
```

### **Bước 5: Reset và Reseed với Volume**
```bash
# Fresh migration với medium dataset
php artisan migrate:fresh --seed

# Fresh migration với specific volume
SEEDER_VOLUME=large php artisan migrate:fresh
SEEDER_VOLUME=large php artisan db:seed --class=ComprehensiveAcademicWorkflowSeeder
```

### **Bước 6: Performance Monitoring**
```bash
# Monitor seeding performance
time SEEDER_VOLUME=large php artisan db:seed --class=ComprehensiveAcademicWorkflowSeeder

# Check database size sau khi seeding
php artisan tinker --execute="
echo 'Database size check:' . PHP_EOL;
echo 'Students: ' . \App\Models\Student::count() . PHP_EOL;
echo 'Attendances: ' . \App\Models\Attendance::count() . PHP_EOL;
echo 'Assessment Scores: ' . \App\Models\AssessmentComponentDetailScore::count() . PHP_EOL;
"
```

### **Bước 7: Verify Data Integrity & Relationships**
```bash
php artisan tinker --execute="
// Verify complex relationships
\App\Models\Student::with('academicRecords.gpaCalculations')->get()->each(function(\$student) {
    echo \$student->full_name . ': ' . \$student->academicRecords->count() . ' records' . PHP_EOL;
});

// Check attendance data integrity
\App\Models\ClassSession::withCount(['attendances'])->get()->each(function(\$session) {
    echo 'Session ' . \$session->id . ': ' . \$session->attendances_count . ' attendances' . PHP_EOL;
});

// Verify grade distributions
\App\Models\AssessmentComponentDetailScore::selectRaw('letter_grade, COUNT(*) as count')
    ->groupBy('letter_grade')
    ->orderBy('letter_grade')
    ->get()
    ->each(function(\$grade) {
        echo 'Grade ' . \$grade->letter_grade . ': ' . \$grade->count . ' scores' . PHP_EOL;
    });
"
```

### **Bước 8: Pagination Testing Commands**
```bash
# Test pagination trên large datasets
php artisan tinker --execute="
// Test student pagination
\$students = \App\Models\Student::paginate(15);
echo 'Page 1 of ' . \$students->lastPage() . ' (Total: ' . \$students->total() . ')' . PHP_EOL;

// Test course offering pagination với filters
\$offerings = \App\Models\CourseOffering::with(['unit', 'instructor', 'semester'])
    ->paginate(20);
echo 'Course offerings: Page 1 of ' . \$offerings->lastPage() . PHP_EOL;

// Test attendance pagination với relationships
\$attendances = \App\Models\Attendance::with(['student', 'classSession.courseOffering'])
    ->paginate(25);
echo 'Attendances: Page 1 of ' . \$attendances->lastPage() . PHP_EOL;
"
```

---

## 📈 **Expected Outcomes**

Sau khi chạy complete seeder workflow, hệ thống sẽ có:

- **Complete Academic Cycle**: From enrollment to graduation
- **Realistic Performance Data**: Grade distributions, attendance patterns
- **Comprehensive Records**: Full audit trail of student progress
- **Academic Analytics**: GPA calculations, academic standing
- **Intervention Points**: Academic holds, probation triggers

Seeder workflow này tạo ra một **simulation hoàn chỉnh và thực tế** của quá trình học tập trong môi trường đại học! 🎓
