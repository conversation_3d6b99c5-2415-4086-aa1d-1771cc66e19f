<script setup lang="ts">
import DataPagination from '@/components/DataPagination.vue';
import DataTable from '@/components/DataTable.vue';
import DebouncedInput from '@/components/DebouncedInput.vue';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { createColumns } from '@/lib/table-utils';
import type { PaginatedResponse } from '@/types';
import type { Program, Semester, Specialization, Student } from '@/types/models';
import { studentRoutes } from '@/utils/routes';
import { Head, router } from '@inertiajs/vue3';
import type { ColumnDef } from '@tanstack/vue-table';
import { Eye, GraduationCap, UserPlus, Users } from 'lucide-vue-next';
import { computed, h, ref } from 'vue';
import { route } from 'ziggy-js';

interface Props {
    students: PaginatedResponse<Student>;
    filters?: {
        search?: string;
        program_id?: number;
        specialization_id?: number;
        sort?: string;
        direction?: string;
        per_page?: number;
    };
    programs: Program[];
    specializations: Specialization[];
    current_campus_id: number;
    current_semester: Semester;
}

const props = defineProps<Props>();

// Reactive data
const data = computed(() => props.students.data);

// Filter state - Initialize with props or defaults
const filters = ref({
    search: props.filters?.search || '',
    program_id: props.filters?.program_id ? props.filters.program_id.toString() : 'all',
    specialization_id: props.filters?.specialization_id ? props.filters.specialization_id.toString() : 'all',
    sort: props.filters?.sort || '',
    direction: props.filters?.direction || 'asc',
    per_page: props.filters?.per_page || 15,
});

// Bulk onboarding dialog
const bulkOnboardingDialog = ref(false);

// Filter specializations based on selected program
const filteredSpecializations = computed(() => {
    if (filters.value.program_id === 'all') {
        return props.specializations;
    }
    return props.specializations.filter((spec) => spec.program_id.toString() === filters.value.program_id);
});

// Column definitions (without selection)
const columns: ColumnDef<Student>[] = [
    {
        header: 'No',
        id: 'no',
        enableSorting: false,
        enableHiding: false,
        cell: ({ row }) => {
            const currentPage = props.students.current_page;
            const perPage = props.students.per_page;
            const rowIndex = row.index;
            return (currentPage - 1) * perPage + rowIndex + 1;
        },
    },
    {
        accessorKey: 'student_id',
        header: 'Student ID',
        enableSorting: true,
        cell: ({ row }) => {
            const student = row.original;
            return h('div', { class: 'font-medium' }, student.student_id);
        },
    },
    {
        accessorKey: 'full_name',
        header: 'Name',
        enableSorting: true,
        cell: ({ row }) => {
            const student = row.original;
            return h('div', { class: 'font-medium' }, student.full_name);
        },
    },
    {
        accessorKey: 'email',
        header: 'Email',
        enableSorting: false,
        cell: ({ row }) => {
            const student = row.original;
            return h('div', { class: 'text-muted-foreground' }, student.email);
        },
    },
    {
        accessorKey: 'program.name',
        header: 'Program',
        enableSorting: false,
        cell: ({ row }) => {
            const student = row.original;
            return h('div', { class: 'text-sm' }, student.program?.name || 'N/A');
        },
    },
    {
        accessorKey: 'specialization.name',
        header: 'Specialization',
        enableSorting: false,
        cell: ({ row }) => {
            const student = row.original;
            return h('div', { class: 'text-sm' }, student.specialization?.name || 'N/A');
        },
    },
    {
        accessorKey: 'status',
        header: 'Status',
        enableSorting: false,
        cell: ({ row }) => {
            const student = row.original;
            const statusColors = {
                active: 'bg-green-100 text-green-800',
                inactive: 'bg-gray-100 text-gray-800',
                suspended: 'bg-red-100 text-red-800',
                graduated: 'bg-blue-100 text-blue-800',
            };
            return h(
                'span',
                {
                    class: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        statusColors[student.status as keyof typeof statusColors] || statusColors.inactive
                    }`,
                },
                student.status,
            );
        },
    },
    {
        id: 'actions',
        header: 'Actions',
        enableHiding: false,
        enableSorting: false,
        cell: 'actions',
    },
];

// Server-side filtering functions following programs/Index.vue pattern
const applyFilters = (newFilters: typeof filters.value) => {
    const params = new URLSearchParams();

    if (newFilters.search) params.set('search', newFilters.search);
    if (newFilters.program_id !== 'all') params.set('program_id', newFilters.program_id);
    if (newFilters.specialization_id !== 'all') params.set('specialization_id', newFilters.specialization_id);
    if (newFilters.sort) params.set('sort', newFilters.sort);
    if (newFilters.direction) params.set('direction', newFilters.direction);
    if (newFilters.per_page) params.set('per_page', newFilters.per_page.toString());

    const url = `/students/new-students${params.toString() ? '?' + params.toString() : ''}`;

    router.visit(url, {
        preserveState: true,
        preserveScroll: true,
        only: ['students', 'filters'],
    });
};

// Update filters handler
const updateFilters = () => {
    applyFilters(filters.value);
};

const clearFilters = () => {
    filters.value = {
        search: '',
        program_id: 'all',
        specialization_id: 'all',
        sort: '',
        direction: 'asc',
        per_page: 15,
    };
    router.visit('/students/new-students', {
        preserveState: true,
        preserveScroll: true,
        only: ['students', 'filters'],
    });
};

// Pagination navigation
const handlePaginationNavigate = (url: string) => {
    router.visit(url, {
        preserveState: true,
        preserveScroll: true,
        only: ['students'],
    });
};

const handlePageSizeChange = (pageSize: number) => {
    filters.value.per_page = pageSize;
    applyFilters(filters.value);
};

// Complete student onboarding - Apply to all filtered students
const executeCompleteOnboarding = () => {
    if (props.students.total === 0) {
        alert('No students found to complete onboarding');
        return;
    }

    router.post(
        '/students/new-students/bulk-onboarding',
        {
            // Send current filters to apply to all matching students
            filters: {
                search: filters.value.search || null,
                program_id: filters.value.program_id !== 'all' ? parseInt(filters.value.program_id) : null,
                specialization_id: filters.value.specialization_id !== 'all' ? parseInt(filters.value.specialization_id) : null,
            },
            semester_id: props.current_semester.id,
        },
        {
            onSuccess: () => {
                bulkOnboardingDialog.value = false;
                // Refresh the page to show updated data
                router.reload({ only: ['students'] });
            },
        },
    );
};
</script>

<template>
    <Head title="New Students" />

    <div class="space-y-6">
        <!-- Header -->
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-semibold tracking-tight">New Students</h1>
                <p class="text-muted-foreground text-sm">Manage first-semester students with filtering and bulk operations</p>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
            <Card>
                <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle class="text-sm font-medium">Total New Students</CardTitle>
                    <Users class="text-muted-foreground h-4 w-4" />
                </CardHeader>
                <CardContent>
                    <div class="text-2xl font-bold">{{ students.total }}</div>
                </CardContent>
            </Card>

            <Card>
                <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle class="text-sm font-medium">Filtered Students</CardTitle>
                    <UserPlus class="text-muted-foreground h-4 w-4" />
                </CardHeader>
                <CardContent>
                    <div class="text-2xl font-bold">{{ students.data.length }}</div>
                    <p class="text-xs text-muted-foreground">on this page</p>
                </CardContent>
            </Card>
        </div>

        <!-- Filters -->
        <Card>
            <CardContent class="pt-6">
                <div class="grid grid-cols-1 gap-4 md:grid-cols-4">
                    <!-- Search -->
                    <DebouncedInput v-model="filters.search" placeholder="Search students..." @debounced="updateFilters" class="max-w-sm" />

                    <!-- Program Filter -->
                    <Select v-model="filters.program_id" @update:model-value="updateFilters">
                        <SelectTrigger>
                            <SelectValue placeholder="Select Program" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">All Programs</SelectItem>
                            <SelectItem v-for="program in programs" :key="program.id" :value="program.id.toString()">
                                {{ program.name }}
                            </SelectItem>
                        </SelectContent>
                    </Select>

                    <!-- Specialization Filter -->
                    <Select v-model="filters.specialization_id" @update:model-value="updateFilters">
                        <SelectTrigger>
                            <SelectValue placeholder="Select Specialization" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">All Specializations</SelectItem>
                            <SelectItem
                                v-for="specialization in filteredSpecializations"
                                :key="specialization.id"
                                :value="specialization.id.toString()"
                            >
                                {{ specialization.name }}
                            </SelectItem>
                        </SelectContent>
                    </Select>

                    <!-- Clear Filters -->
                    <Button variant="outline" @click="clearFilters"> Clear Filters </Button>
                </div>
            </CardContent>
        </Card>

        <!-- Bulk Operations -->
        <Card>
            <CardContent class="pt-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-sm font-medium">Bulk Operations</h3>
                        <p class="text-xs text-muted-foreground">Complete onboarding for all {{ students.total }} filtered new students</p>
                    </div>

                    <div class="flex gap-2">
                        <!-- Complete Student Onboarding Dialog -->
                        <Dialog v-model:open="bulkOnboardingDialog">
                            <DialogTrigger as-child>
                                <Button variant="default" size="sm">
                                    <GraduationCap class="mr-2 h-4 w-4" />
                                    Complete Student Onboarding
                                </Button>
                            </DialogTrigger>
                            <DialogContent>
                                <DialogHeader>
                                    <DialogTitle>Complete Student Onboarding</DialogTitle>
                                </DialogHeader>
                                <div class="space-y-4">
                                    <div class="space-y-2">
                                        <p class="text-sm text-muted-foreground">
                                            This will automatically create:
                                        </p>
                                        <ul class="text-sm text-muted-foreground space-y-1 ml-4">
                                            <li>• Semester enrollments for all filtered students</li>
                                            <li>• Course offerings for first-year units</li>
                                            <li>• Course registrations linking students to courses</li>
                                        </ul>
                                    </div>
                                    <div>
                                        <label class="text-sm font-medium">Semester: </label>
                                        <span class="text-muted-foreground text-sm"> {{ current_semester.name }} - {{ current_semester.code }} </span>
                                    </div>
                                    <div class="flex justify-end gap-2">
                                        <Button variant="outline" @click="bulkOnboardingDialog = false"> Cancel </Button>
                                        <Button @click="executeCompleteOnboarding"> Complete Onboarding </Button>
                                    </div>
                                </div>
                            </DialogContent>
                        </Dialog>
                    </div>
                </div>
            </CardContent>
        </Card>

        <!-- Data Table -->
        <DataTable :data="data" :columns="columns">
            <template #cell-actions="{ row }">
                <div class="flex items-center gap-2">
                    <TooltipProvider :delay-duration="0" ignore-non-keyboard-focus disable-hoverable-content>
                        <Tooltip>
                            <TooltipTrigger as-child>
                                <Button variant="ghost" size="sm" @click="router.visit(studentRoutes.show(row.original.id))" title="View student">
                                    <Eye class="h-4 w-4" />
                                </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>View student</p>
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                </div>
            </template>
        </DataTable>

        <!-- Pagination -->
        <DataPagination :pagination-data="students" @navigate="handlePaginationNavigate" @page-size-change="handlePageSizeChange" />
    </div>
</template>
