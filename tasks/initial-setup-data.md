## **TRƯỜNG – CƠ SỞ – MÔN HỌC – GIẢNG VIÊN**

Giai đoạn này nên chạy **trước khi tạo sinh viên**, và có thể tách thành các Seeder sau:

### **Danh sách Seeder & <PERSON>ai trò**

| STT | Seeder | Mô tả |
| --- | --- | --- |
| 001 | `InstitutionSetupSeeder.php` | Tạo hệ thống campus, tòa nhà, phòng học |
| 002 | `RoleAndPermissionSeeder.php` | Tạo danh sách quyền và gán role cho user |
| 003 | `UserAccountSeeder.php` | Tạo tài khoản Super Admin, Admin, Staff |
| 004 | `LecturerSeeder.php` | Tạo user + hồ sơ giảng viên |
| 005 | `AcademicStructureSeeder.php` | Tạo chương trình học, chuyê<PERSON> ngành, môn học |
| 006 | `CurriculumSeeder.php` | Gắn môn học vào chương trình + yêu cầu tốt nghiệp |
| 007 | `SemesterAndOfferingSeeder.php` | Tạo học kỳ, lớp học, gán giảng viên |
| 008 | `SyllabusSeeder.php` | Gắn syllabus & bài đánh giá cho mỗi lớp học |

## Chi tiết từng Seeder

### `InstitutionSetupSeeder.php`

- Tạo các cơ sở (`Campuses`) như:
    - Swinburne Vietnam - Hanoi Campus
    - Swinburne Vietnam - HCMC Campus
- Mỗi campus:
    - 2–3 tòa nhà (`buildings`)
    - 10+ phòng học (`rooms`) phân loại: Lecture Hall, Lab, Tutorial Room

### `UserRoleAndModelSeeder.php`

- Tạo danh sách permissions (lấy từ file `config/permission.php`)
- Tạo các roles: super_admin, admin, ...
- Gán quyền cho từng role
- Tạo user mẫu
- Gán role tương ứng cho user

### `UserAccountSeeder.php`

- Tạo user đặc biệt:
    - 1 Super Admin
    - 2 Admin mỗi campus
    - 3–5 Staff
- Gán role và campus tương ứng

### `LecturerSeeder.php`

- Tạo 20 giảng viên:
    - User + profile `lecturers`
    - Gán campus, bộ môn (department)

### `AcademicStructureSeeder.php`

- Tạo ngành học (`programs`) như:
    - Bachelor of IT
    - Bachelor of Business
- Tạo chuyên ngành (`specializations`):
    - Software Development
    - Cybersecurity
- Tạo môn học (`units`):
    - 30–40 môn
    - Thêm prerequisite (VD: `PROG101` → `PROG201`)
    - Thêm equivalent units nếu có
    - Một số môn yêu cầu tín chỉ (`unit_prerequisite_groups`, `unit_prerequisite_conditions`)

### `CurriculumSeeder.php`

- Tạo phiên bản chương trình (`curriculum_versions`)
- Gắn môn học vào chương trình (`curriculum_units`)
    - Phân loại bắt buộc / tự chọn
- Tạo yêu cầu tốt nghiệp:
    - Tổng tín chỉ
    - Tín chỉ chuyên ngành
    - Môn đặc biệt bắt buộc (capstone...)

### `SemesterAndOfferingSeeder.php`

- Tạo học kỳ:
    - `FALL2024`, `SPRING2025`, `SUMMER2025`
- Tạo lớp học (`course_offerings`)
    - Gắn môn học, học kỳ, campus
    - Gán giảng viên
    - Gán phòng học

### `SyllabusSeeder.php`

- Tạo syllabus cho từng môn học:
    - Thông tin mô tả môn
    - Phân bố thời gian
- Tạo các thành phần đánh giá (`assessment_components`)
    - Assignment (20%), Midterm (30%), Final Exam (50%)

## Gợi ý thứ tự chạy

```php
$this->call([
    InstitutionSetupSeeder::class,
    RoleAndPermissionSeeder::class,
    UserAccountSeeder::class,
    LecturerSeeder::class,
    AcademicStructureSeeder::class,
    CurriculumSeeder::class,
    SemesterAndOfferingSeeder::class,
    SyllabusSeeder::class,
]);

```
