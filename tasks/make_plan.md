I have:
- A fixed admin menu structure (as a sidebar tree),
- A fully seeded education management database,
- Using Laravel for backend APIs and Vue 3 + Inertia.js for the frontend.

Please generate a **complete implementation plan** to turn the entire menu into functional modules:

1. Identify each functional module based on the menu structure.
2. For each module, specify:
   - The main data to display (which tables/fields),
   - Supported CRUD operations (if applicable),
   - Suggested Laravel API routes (RESTful or service-resource style),
   - Vue 3 component structure (file names, layout suggestions).
3. Provide a recommended implementation order to optimize development flow and minimize dependencies.
4. Clearly mark any menu items that are purely structural (e.g., group titles without functionality).
5. Propose a frontend folder/component structure that supports maintainability and scalability.

Note: This plan is intended for task breakdown and team coordination. Please write it clearly, in a format suitable for copy-pasting into a project management tool.
