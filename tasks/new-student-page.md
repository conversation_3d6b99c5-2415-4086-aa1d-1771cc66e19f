Create a new "New Students" page in the student management menu system. This involves:

1. **Menu Configuration**: Add a new menu item to the sidebar menu configuration file at `resources/js/constants/menu-sidebar.ts` under the student management section.

2. **Page Implementation**: Create a new page/component to display a list of first-semester new students with the following features:

   **Filtering Capabilities:**
   - Filter by major/program (ngành)
   - Filter by specialization (chuyên ngành) 
   - Filter by student name
   - Filter by student ID

   **Data Display:**
   - Show list of students who are in their first semester
   - Display relevant student information (name, ID, major, specialization, etc.)

   **Bulk Operations:**
   - Implement functionality to create enrollment records for all displayed students
   - Implement functionality to create course offering records for all displayed students  
   - Implement functionality to create course registration records for all displayed students
   - These operations should work on the entire filtered list of students

3. **Technical Requirements:**
   - Follow Laravel + Vue.js architecture patterns
   - Use proper TypeScript interfaces for data structures
   - Implement proper form validation for bulk operations
   - Use appropriate UI components (likely Shadcn/UI + TailwindCSS)
   - Follow <PERSON><PERSON> naming conventions for routes, controllers, and models
   - Ensure proper error handling and user feedback for bulk operations

4. **Database Considerations:**
   - Ensure proper relationships between Student, Enrollment, CourseOffering, and CourseRegistration models
   - Implement efficient queries for filtering and bulk operations
   - Consider transaction handling for bulk operations to maintain data integrity
