## **TIMELINE SEEDER THEO GIAI ĐOẠN HỌC TẬP**

| STT | Seeder | Sự kiện mô phỏng | <PERSON><PERSON> tả & Trường hợp |
| --- | --- | --- | --- |

### **Giai đoạn Khởi tạo**

`CreateActiveStudentsSeeder.php` | Tạo 500 sinh viên đầu vào | Thông tin cơ bản, campus, trạng thái `active`

### **Giai đo<PERSON>n <PERSON>nh & Học kỳ đầu tiên (FALL2024)**

`EnrollStudentsToProgramSeeder.php` | Ghi danh chương trình | Ghi danh vào ngành học + curriculum version

`Fall2024OfferingSeeder.php` | Tạo lớp học kỳ FALL2024 | Tạo `course_offerings`, gán giảng viên, phòng học cho kỳ đầu tiên. Logic dựa trên `SemesterAndOfferingSeeder` trong `initial-setup-data.md`.

`Fall2024SyllabusSeeder.php` | Gắn syllabus cho lớp học | Gắn syllabus và các `assessment_components` cho các lớp học của kỳ FALL2024. Logic dựa trên `SyllabusSeeder`.

`InitialCourseRegistrationSeeder.php` | Đăng ký môn học kỳ đầu | Chia sinh viên thành nhóm: theo thứ tự id

| 05 | `005_ClassSessionsSeeder.php` | Tạo lịch học | Tạo `class_sessions` cụ thể theo ngày/tuần cho từng lớp |

| 06 | `006_AttendanceSeeder.php` | Điểm danh | Sinh viên đi học đầy đủ, nghỉ có lý do, trốn học (fail vì vắng) |

| 07 | `007_AssessmentSeeder.php` | Chấm điểm thành phần | Nhập điểm bài kiểm tra (assignment, midterm, final) |

| 08 | `008_AcademicRecordSeeder.php` | Ghi điểm cuối kỳ | Tổng kết kết quả môn học: PASS / FAIL / AB |

| 09 | `009_GpaCalculationSeeder.php` | Tính GPA | Tính GPA cho kỳ đầu + đánh giá học lực |

| 10 | `010_AcademicStandingSeeder.php` | Học lực | Gán trạng thái: Good standing, Warning, Probation |

### **Kiểm tra điều kiện học kỳ mới (SPRING2025)**

| 11 | `011_RegistrationEligibilitySeeder.php` | Kiểm tra đăng ký hợp lệ | Gắn điều kiện tiên quyết, tín chỉ tích lũy, học lực |

| 12a | `Spring2025OfferingSeeder.php` | Tạo lớp học kỳ SPRING2025 | Tạo `course_offerings` cho kỳ học thứ hai. |

| 12b | `Spring2025SyllabusSeeder.php` | Gắn syllabus cho lớp học | Gắn syllabus và `assessment_components` cho các lớp học của kỳ SPRING2025. |

| 12c | `012_SecondSemesterRegistrationSeeder.php` | Đăng ký học kỳ 2 | Mô phỏng các tình huống: đủ điều kiện, không đủ (fail prereq, thiếu tín chỉ) |

| 13 | `013_RetakeAndRepeatSeeder.php` | Đăng ký học lại | Sinh viên đăng ký lại môn đã trượt |

| 14 | `014_UnitExemptionSeeder.php` | Miễn học / quy đổi | Một số sinh viên được miễn học nhờ chứng chỉ/credit transfer |

| 15 | `015_AcademicLeaveSeeder.php` | Bảo lưu học tập | Một số sinh viên xin nghỉ tạm thời kỳ này |

| 16 | `016_SecondSemesterAttendanceSeeder.php` | Đi học kỳ 2 | Nhập điểm danh |

| 17 | `017_SecondSemesterAssessmentSeeder.php` | Kiểm tra & chấm điểm | Điểm từng môn kỳ 2 |

| 18 | `018_SecondSemesterGpaSeeder.php` | Tính GPA học kỳ 2 | Tổng GPA & đánh giá học lực lại |

### **Giai đoạn lặp lại & nâng cao (SUMMER2025...)**

| 19 | `019_AdvancedUnitEligibilitySeeder.php` | Kiểm tra môn nâng cao | Các môn như `CAPSTONE`, `INTERNSHIP`, `ADVANCED_RESEARCH` yêu cầu đủ tín chỉ |

| 20 | `020_AdvancedUnitRegistrationSeeder.php` | Đăng ký môn nâng cao | Một số sinh viên được phép đăng ký, một số bị chặn |

| 21 | `021_MixedSemesterSeeder.php` | Mô phỏng học kỳ phức tạp | Một số SV học môn thường + môn nâng cao, retake, leave xen kẽ |

| 22 | `022_SemesterCompletionSeeder.php` | Hoàn tất kỳ cuối | Ghi điểm, GPA |

### **Tốt nghiệp**

| 23 | `023_GraduationEligibilitySeeder.php` | Đánh giá tốt nghiệp | Tính tổng tín chỉ, điều kiện bắt buộc |

| 24 | `024_GraduationApplicationSeeder.php` | Sinh viên nộp đơn tốt nghiệp | Tạo bản ghi `graduation_requests` |

| 25 | `025_GraduationApprovalSeeder.php` | Phê duyệt tốt nghiệp | SV được chấp nhận hoặc từ chối, gán trạng thái `graduated` |

### **Trường hợp bất thường**

| 26 | `026_StudentDropoutSeeder.php` | Thôi học | Một số sinh viên tự rút hoặc buộc thôi học |

| 27 | `027_StudentTransferSeeder.php` | Chuyển ngành | Sinh viên đổi chương trình học |

| 28 | `028_StudentReenrollmentSeeder.php` | Học lại sau khi bảo lưu | Quay lại và học tiếp |

| 29 | `029_FinancialScholarshipSeeder.php` | Học bổng | Một số sinh viên nhận học bổng theo GPA |

| 30 | `030_StudentAuditTrailSeeder.php` | Ghi log quá trình học | Tạo bản ghi nhật ký học tập từng sinh viên

## 📁 TỔ CHỨC THƯ MỤC GỢI Ý

```bash
bash
CopyEdit
/database
  /seeders
    /Timeline
      001_CreateInitialStudentsSeeder.php
      ...
      030_StudentAuditTrailSeeder.php
    TimelineSeederRunner.php

```

## 📌 TỔNG KẾT
