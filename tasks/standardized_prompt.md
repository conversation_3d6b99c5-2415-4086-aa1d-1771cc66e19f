# AI Code Generation Prompt Template for Laravel + Vue.js Projects

---

## 1. Context Analysis Requirements

**Before generating any code, you must:**
- Analyze the current codebase structure, including:
  - Directory and file organization for both Laravel (PHP) and Vue.js (TypeScript)
  - Existing coding patterns, naming conventions, and architectural decisions (e.g., service layers, resource patterns, state management)
  - Database schema, relationships, and migrations
  - API endpoint structure and data formats
- Use available tools (e.g., code search, symbol analysis, file reading) to:
  - Identify relevant models, controllers, services, requests, resources, and Vue components
  - Understand how similar features are implemented
  - Detect any reusable utilities, traits, or mixins
- Summarize your findings and reference them in your implementation plan.

---

## 2. Code Quality Standards

**All generated code must:**
- **PHP (Laravel):**
  - Strictly follow [PSR-12](https://www.php-fig.org/psr/psr-12/) coding standards
  - Use clear, consistent naming and proper namespace organization
  - Include comprehensive PHPDoc blocks for all classes, methods, and properties
  - Implement robust error handling and input validation (preferably via FormRequest classes)
- **TypeScript (Vue 3):**
  - Use TypeScript with the Vue 3 Composition API
  - Follow established component structure and naming conventions
  - Include JSDoc/TSDoc comments for all functions, props, emits, and complex logic
  - Ensure proper error handling, loading states, and user feedback
- **General:**
  - Avoid code duplication; leverage existing utilities and patterns
  - Write clean, maintainable, and readable code

---

## 3. Integration Guidelines

**Ensure new code:**
- Integrates with existing Laravel models, controllers, services, and policies
- Follows the current Vue component architecture and state management approach (e.g., Pinia, Vuex, or composables)
- Respects the established database schema, relationships, and constraints
- Uses and extends existing API endpoints and data structures where possible
- Maintains compatibility with frontend route management and backend route definitions
- Adheres to project-specific conventions (e.g., Service-Request-Resource pattern, Inertia.js usage, validation strategies)

---

## 4. Testing Requirements

**For every new feature or change, you must:**
- Generate appropriate test cases:
  - **PHP:** Unit and feature tests using Laravel's testing framework (e.g., PHPUnit, Pest)
  - **Vue:** Component and composable tests using the project's preferred testing tools (e.g., Vitest, Vue Test Utils)
- Ensure tests cover:
  - Core functionality and edge cases
  - Error handling and validation
  - Integration with related modules/components
- Provide clear instructions or code snippets for running the tests

---

## 5. Review Checklist

**Before presenting the final code, verify that:**
- [ ] All code adheres to PSR-12 (PHP) and TypeScript best practices
- [ ] Comprehensive documentation (PHPDoc, TSDoc) is present
- [ ] Error handling and validation are robust and consistent
- [ ] Code integrates seamlessly with existing models, services, components, and routes
- [ ] Database interactions respect schema and relationships
- [ ] All relevant test cases are included and pass successfully
- [ ] No security vulnerabilities or performance bottlenecks are introduced
- [ ] Code is clean, maintainable, and follows project conventions
- [ ] All dependencies and imports are correct and necessary
- [ ] The solution is production-ready and requires minimal developer revision

---

**Emphasize:**  
- Understanding and referencing the current codebase context is mandatory before code generation.  
- The final output must be ready for direct integration and deployment, minimizing the need for further developer review.

---

**Use this template as the standard for all AI-generated code in Laravel + Vue.js projects.**
